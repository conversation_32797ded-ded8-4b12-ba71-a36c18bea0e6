{"version": "0.2.0", "configurations": [{"name": "Python Debugger: Flask", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/app.py", "env": {"FLASK_DEBUG": "1", "FLASK_ENV": "development", "PORT": "5001"}, "console": "integratedTerminal", "jinja": true, "autoStartBrowser": false, "cwd": "${workspaceFolder}"}, {"name": "Flask Debug with SAP Environment", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/app.py", "env": {"FLASK_DEBUG": "1", "FLASK_ENV": "development", "PORT": "5001"}, "console": "integratedTerminal", "jinja": true, "autoStartBrowser": false, "cwd": "${workspaceFolder}", "envFile": "${workspaceFolder}/.env"}, {"name": "Flask Module Debug", "type": "debugpy", "request": "launch", "module": "flask", "env": {"FLASK_APP": "app.py", "FLASK_DEBUG": "1", "FLASK_ENV": "development"}, "args": ["run", "--host=0.0.0.0", "--port=5001", "--debug"], "jinja": true, "autoStartBrowser": false, "console": "integratedTerminal", "cwd": "${workspaceFolder}", "envFile": "${workspaceFolder}/.env"}, {"name": "Attach to Flask Debug Server", "type": "debugpy", "request": "attach", "connect": {"host": "localhost", "port": 5678}, "jinja": true, "justMyCode": false, "pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "."}]}]}