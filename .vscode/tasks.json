{"version": "2.0.0", "tasks": [{"label": "Load SAP Environment", "type": "shell", "command": "source", "args": ["./sap_env.sh"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Start Debug Server", "type": "shell", "command": "./debug.sh", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Test SAP Connection", "type": "shell", "command": "python3", "args": ["test_pyrfc.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}, {"label": "Run All Tests", "type": "shell", "command": "python3", "args": ["test_complete_workflow.py"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new"}, "problemMatcher": []}]}