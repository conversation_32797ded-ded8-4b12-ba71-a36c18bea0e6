#!/usr/bin/env python3
"""
Script to convert sap_env.sh to .env file for VS Code debugging.
"""

import os
import re

def convert_sap_env_to_dotenv():
    """Convert sap_env.sh to .env format for VS Code."""
    
    sap_env_file = "sap_env.sh"
    dotenv_file = ".env"
    
    if not os.path.exists(sap_env_file):
        print(f"❌ {sap_env_file} not found")
        print("Please run: python3 setup_sap_connection.py")
        return False
    
    print(f"Converting {sap_env_file} to {dotenv_file} for VS Code...")
    
    env_vars = {}
    
    # Read sap_env.sh and extract environment variables
    with open(sap_env_file, 'r') as f:
        for line in f:
            line = line.strip()
            
            # Look for export statements
            if line.startswith('export '):
                # Remove 'export ' prefix
                line = line[7:]
                
                # Parse VAR=value format
                if '=' in line:
                    var, value = line.split('=', 1)
                    
                    # Remove quotes if present
                    value = value.strip('"\'')
                    
                    env_vars[var] = value
    
    if not env_vars:
        print("❌ No environment variables found in sap_env.sh")
        return False
    
    # Write .env file
    with open(dotenv_file, 'w') as f:
        f.write("# SAP Environment Variables for VS Code Debugging\n")
        f.write("# Generated from sap_env.sh\n\n")
        
        # Add Flask debug settings
        f.write("# Flask Debug Settings\n")
        f.write("FLASK_DEBUG=1\n")
        f.write("FLASK_ENV=development\n")
        f.write("PORT=5001\n\n")
        
        # Add SAP variables
        f.write("# SAP Connection Parameters\n")
        for var, value in env_vars.items():
            f.write(f"{var}={value}\n")
    
    print(f"✅ Created {dotenv_file} with {len(env_vars)} SAP variables")
    print("\nVariables converted:")
    for var in env_vars.keys():
        print(f"  - {var}")
    
    print(f"\n🎯 VS Code debugging is now ready!")
    print("Use the 'Flask Debug with SAP Environment' configuration")
    
    return True

if __name__ == '__main__':
    convert_sap_env_to_dotenv()
