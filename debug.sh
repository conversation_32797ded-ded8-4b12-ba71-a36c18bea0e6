#!/bin/bash

# Debug script for Measuring Point Upload Web Application
# This script sets up the environment and starts the app in debug mode

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_debug() {
    echo -e "${PURPLE}[DEBUG]${NC} $1"
}

print_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Main debug function
main() {
    print_header "=================================================="
    print_header "  Measuring Point Upload - DEBUG MODE"
    print_header "=================================================="
    
    # Load SAP environment if available
    if [ -f "sap_env.sh" ]; then
        print_info "Loading SAP environment variables..."
        source ./sap_env.sh

        # Create .env file for VS Code debugging if it doesn't exist
        if [ ! -f ".env" ]; then
            print_info "Creating .env file for VS Code debugging..."
            python3 create_env_for_vscode.py >/dev/null 2>&1
        fi

        # Verify SAP variables are loaded
        if [ -z "$SAP_ASHOST" ] || [ -z "$SAP_USER" ]; then
            print_warning "SAP environment variables not properly loaded"
            print_warning "Application will run with mock SAP functionality"
        else
            print_info "SAP environment loaded successfully"
            print_debug "SAP Host: $SAP_ASHOST"
            print_debug "SAP Client: $SAP_CLIENT"
            print_debug "SAP User: $SAP_USER"
        fi
    else
        print_warning "No sap_env.sh found - using mock SAP functionality"
    fi
    
    # Set debug environment variables
    export FLASK_APP=app.py
    export FLASK_DEBUG=1
    export FLASK_ENV=development
    
    
    # Find available port for debugging
    DEBUG_PORT=5001
    while lsof -Pi :$DEBUG_PORT -sTCP:LISTEN -t >/dev/null 2>&1; do
        print_warning "Port $DEBUG_PORT is busy, trying $((DEBUG_PORT + 1))"
        DEBUG_PORT=$((DEBUG_PORT + 1))
    done
    
    export PORT=$DEBUG_PORT
    
    print_info "Debug configuration:"
    print_debug "  FLASK_APP: $FLASK_APP"
    print_debug "  FLASK_DEBUG: $FLASK_DEBUG"
    print_debug "  FLASK_ENV: $FLASK_ENV"
    print_debug "  PORT: $PORT"
    
    # Check Python dependencies
    print_info "Checking Python dependencies..."
    missing_deps=()
    
    if ! python3 -c "import flask" 2>/dev/null; then
        missing_deps+=("flask")
    fi
    
    if ! python3 -c "import pandas" 2>/dev/null; then
        missing_deps+=("pandas")
    fi
    
    if ! python3 -c "import openpyxl" 2>/dev/null; then
        missing_deps+=("openpyxl")
    fi
    
    if [ ${#missing_deps[@]} -gt 0 ]; then
        print_warning "Missing dependencies: ${missing_deps[*]}"
        print_info "Installing missing dependencies..."
        pip3 install "${missing_deps[@]}"
    else
        print_info "All basic dependencies are available"
    fi
    
    # Check PyRFC separately
    if python3 -c "import pyrfc" 2>/dev/null; then
        print_info "PyRFC is available - real SAP connection will be used"
    else
        print_warning "PyRFC not available - mock SAP functionality will be used"
    fi
    
    # Check if VS Code launch.json exists
    if [ -f ".vscode/launch.json" ]; then
        print_info "VS Code launch configuration found"
        print_debug "You can also debug using VS Code:"
        print_debug "  1. Open VS Code in this directory"
        print_debug "  2. Go to Run and Debug (Ctrl+Shift+D)"
        print_debug "  3. Select 'Python Debugger: Flask'"
        print_debug "  4. Press F5 to start debugging"
    fi
    
    print_header "=================================================="
    print_header "  Starting Flask Application in DEBUG MODE"
    print_header "=================================================="
    
    print_info "Debug features enabled:"
    print_debug "  ✅ Auto-reload on code changes"
    print_debug "  ✅ Detailed error pages with stack traces"
    print_debug "  ✅ Enhanced logging"
    print_debug "  ✅ Development server warnings"
    
    print_info "Application will be available at: http://localhost:$DEBUG_PORT"
    print_info "Press Ctrl+C to stop the debug server"
    
    echo ""
    print_header "Debug Server Starting..."
    print_header "=================================================="
    
    # Start the Flask application in debug mode
    if [ "$ATTACH_MODE" = true ]; then
        print_info "Starting Flask with debugpy for VS Code attachment..."
        print_info "Debugger will listen on port 5678"
        print_info "In VS Code: Use 'Attach to Flask Debug Server' configuration and press F5"
        python3 -m debugpy --listen 5678 --wait-for-client -m flask run --host=0.0.0.0 --port=$DEBUG_PORT --debug
    else
        print_info "Starting Flask in regular debug mode..."
        print_info "For VS Code debugging with breakpoints, use: $0 --attach"
        python3 -m flask run --host=0.0.0.0 --port=$DEBUG_PORT --debug
    fi
}

# Handle command line arguments
ATTACH_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Debug script for Measuring Point Upload Web Application"
            echo ""
            echo "Options:"
            echo "  -h, --help     Show this help message"
            echo "  -p, --port     Specify debug port number (default: 5001)"
            echo "  -v, --verbose  Enable verbose debug output"
            echo "  --attach       Start with debugpy for VS Code attachment"
            echo ""
            echo "Features:"
            echo "  • Loads SAP environment variables"
            echo "  • Sets up Flask debug mode"
            echo "  • Auto-reload on code changes"
            echo "  • Detailed error pages"
            echo "  • Enhanced logging"
            echo ""
            echo "VS Code Integration:"
            echo "  • Compatible with .vscode/launch.json"
            echo "  • Use --attach flag for VS Code debugging"
            echo ""
            echo "Examples:"
            echo "  $0              # Start debug server"
            echo "  $0 -p 8080      # Start on port 8080"
            echo "  $0 --attach     # Start with VS Code debugger support"
            exit 0
            ;;
        -p|--port)
            if [ -z "$2" ]; then
                print_error "Port number required"
                exit 1
            fi
            DEBUG_PORT=$2
            shift 2
            ;;
        -v|--verbose)
            set -x  # Enable verbose mode
            shift
            ;;
        --attach)
            ATTACH_MODE=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            print_error "Use -h or --help for usage information"
            exit 1
            ;;
    esac
done

# Run main function
main "$@"
