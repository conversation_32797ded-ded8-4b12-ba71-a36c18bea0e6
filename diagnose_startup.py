#!/usr/bin/env python3
"""
Diagnostic script to identify startup issues.
"""

import sys
import os
import traceback

def diagnose_startup():
    """Diagnose common startup issues."""
    print("=" * 60)
    print("STARTUP DIAGNOSTIC")
    print("=" * 60)
    
    issues_found = []
    
    # Check Python version
    print(f"Python version: {sys.version}")
    if sys.version_info < (3, 8):
        issues_found.append("Python version too old (need 3.8+)")
    else:
        print("✅ Python version OK")
    
    # Check required modules
    required_modules = ['flask', 'pandas', 'openpyxl']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} - OK")
        except ImportError as e:
            print(f"❌ {module} - MISSING: {e}")
            missing_modules.append(module)
            issues_found.append(f"Missing module: {module}")
    
    # Check PyRFC (optional)
    try:
        import pyrfc
        print("✅ pyrfc - OK (real SAP connection available)")
    except ImportError as e:
        print(f"⚠️  pyrfc - MISSING: {e}")
        print("   Will use mock SAP functionality")
    
    # Check if app.py exists and can be imported
    if not os.path.exists('app.py'):
        print("❌ app.py not found")
        issues_found.append("app.py file missing")
    else:
        print("✅ app.py found")
        
        # Try to import app module
        try:
            sys.path.insert(0, '.')
            import app
            print("✅ app.py imports successfully")
            
            # Check if Flask app is created
            if hasattr(app, 'app'):
                print("✅ Flask app object found")
            else:
                print("❌ Flask app object not found")
                issues_found.append("Flask app not properly initialized")
                
        except Exception as e:
            print(f"❌ Error importing app.py: {e}")
            print("Full traceback:")
            traceback.print_exc()
            issues_found.append(f"app.py import error: {e}")
    
    # Check environment variables
    print("\nEnvironment variables:")
    env_vars = ['FLASK_DEBUG', 'FLASK_ENV', 'PORT']
    for var in env_vars:
        value = os.environ.get(var, 'Not set')
        print(f"  {var}: {value}")
    
    # Check SAP environment
    sap_vars = ['SAP_ASHOST', 'SAP_USER', 'SAP_CLIENT']
    sap_configured = False
    for var in sap_vars:
        value = os.environ.get(var, 'Not set')
        if value != 'Not set':
            sap_configured = True
        print(f"  {var}: {'***' if 'PASSWD' in var else value}")
    
    if sap_configured:
        print("✅ SAP environment partially configured")
    else:
        print("⚠️  SAP environment not configured (will use mock)")
    
    # Check port availability
    port = int(os.environ.get('PORT', 5001))
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"⚠️  Port {port} is already in use")
            issues_found.append(f"Port {port} is busy")
        else:
            print(f"✅ Port {port} is available")
    except Exception as e:
        print(f"⚠️  Could not check port {port}: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("DIAGNOSTIC SUMMARY")
    print("=" * 60)
    
    if not issues_found:
        print("🎉 No critical issues found!")
        print("The app should start normally.")
    else:
        print(f"❌ Found {len(issues_found)} issues:")
        for i, issue in enumerate(issues_found, 1):
            print(f"  {i}. {issue}")
        
        print("\nSuggested fixes:")
        if missing_modules:
            print(f"• Install missing modules: pip3 install {' '.join(missing_modules)}")
        if any("Port" in issue for issue in issues_found):
            print("• Change port in .env file or kill process using the port")
        if any("app.py" in issue for issue in issues_found):
            print("• Check app.py for syntax errors or missing imports")
    
    return len(issues_found) == 0

def test_minimal_flask():
    """Test if Flask can start with minimal configuration."""
    print("\n" + "=" * 60)
    print("MINIMAL FLASK TEST")
    print("=" * 60)
    
    try:
        from flask import Flask
        test_app = Flask(__name__)
        
        @test_app.route('/')
        def hello():
            return "Test Flask app is working!"
        
        print("✅ Minimal Flask app created successfully")
        print("Try running: python3 -c \"from flask import Flask; app=Flask(__name__); app.run(port=5002, debug=True)\"")
        return True
        
    except Exception as e:
        print(f"❌ Minimal Flask test failed: {e}")
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Diagnosing startup issues...")
    print("This will help identify why the Flask app crashes on startup.")
    print()
    
    success = diagnose_startup()
    test_minimal_flask()
    
    if not success:
        print("\n🔧 NEXT STEPS:")
        print("1. Fix the issues listed above")
        print("2. Try running: python3 app.py")
        print("3. Check the error messages in the terminal")
        print("4. If still failing, run with verbose output: python3 -v app.py")
    else:
        print("\n🎯 DEBUGGING TIPS:")
        print("1. Set breakpoints in VS Code at the start of app.py")
        print("2. Check the Debug Console for error messages")
        print("3. Try the 'Flask Debug with SAP Environment' configuration")
        print("4. Use the integrated terminal to see full output")
