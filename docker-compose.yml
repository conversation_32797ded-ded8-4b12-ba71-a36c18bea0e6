version: '3.8'

services:
  sap-rfc-test:
    build: .
    container_name: sap-rfc-test
    environment:
      - RFC_NO_DECIMAL_CONV=1
      - RFC_TRACE=0
      - PYTHONUNBUFFERED=1
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./traces:/app/traces
    ports:
      - "5001:5001"
    working_dir: /app
    stdin_open: true
    tty: true
    command: bash

  # Alternative service with RFC tracing enabled
  sap-rfc-test-trace:
    build: .
    container_name: sap-rfc-test-trace
    environment:
      - RFC_NO_DECIMAL_CONV=1
      - RFC_TRACE=1
      - PYTHONUNBUFFERED=1
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./traces:/app/traces
    ports:
      - "5002:5001"
    working_dir: /app
    stdin_open: true
    tty: true
    command: bash

  # Service for testing different PyRFC versions
  sap-rfc-test-alt:
    build: .
    container_name: sap-rfc-test-alt
    environment:
      - RFC_NO_DECIMAL_CONV=0  # Test with decimal conversion enabled
      - RFC_TRACE=1
      - PYTHONUNBUFFERED=1
    volumes:
      - .:/app
      - ./logs:/app/logs
      - ./traces:/app/traces
    ports:
      - "5003:5001"
    working_dir: /app
    stdin_open: true
    tty: true
    command: bash
