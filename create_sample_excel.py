"""
<PERSON><PERSON><PERSON> to create a sample Excel file for testing the measuring point upload application.
"""

import pandas as pd
from datetime import datetime

# Sample data matching the expected format
sample_data = [
    {
        'Meas-Point': '1000152',
        'Manufacturer': 'KOMATSU',
        'Model': '830E-1AC',
        'Unit ID': '201',
        'Hours': 65024,
        'Last Entry Date': '2/2/25',
        'Last Entry Time': '8:40AM',
        'INVESTIGATE': '155',
        'DEPARTMENT': 'Mine Ops'
    },
    {
        'Meas-Point': '1000153',
        'Manufacturer': 'KOMATSU',
        'Model': '830E-1AC',
        'Unit ID': '202',
        'Hours': 66038,
        'Last Entry Date': '2/2/25',
        'Last Entry Time': '8:40AM',
        'INVESTIGATE': '155',
        'DEPARTMENT': 'Mine Ops'
    },
    {
        'Meas-Point': '1000154',
        'Manufacturer': 'CATERPILLAR',
        'Model': '797F',
        'Unit ID': '203',
        'Hours': 45678,
        'Last Entry Date': '2/3/25',
        'Last Entry Time': '9:15AM',
        'INVESTIGATE': '160',
        'DEPARTMENT': 'Mine Ops'
    },
    {
        'Meas-Point': '1000155',
        'Manufacturer': 'LIEBHERR',
        'Model': 'T 282C',
        'Unit ID': '204',
        'Hours': 52341,
        'Last Entry Date': '2/3/25',
        'Last Entry Time': '10:30AM',
        'INVESTIGATE': '165',
        'DEPARTMENT': 'Mine Ops'
    },
    {
        'Meas-Point': '1000156',
        'Manufacturer': 'KOMATSU',
        'Model': '930E-4SE',
        'Unit ID': '205',
        'Hours': 38912,
        'Last Entry Date': '2/4/25',
        'Last Entry Time': '7:45AM',
        'INVESTIGATE': '170',
        'DEPARTMENT': 'Mine Ops'
    }
]

# Create DataFrame
df = pd.DataFrame(sample_data)

# Save to Excel file
filename = 'sample_measuring_points.xlsx'
df.to_excel(filename, index=False, engine='openpyxl')

print(f"Sample Excel file created: {filename}")
print(f"Contains {len(sample_data)} sample records")
print("\nColumns:")
for col in df.columns:
    print(f"  - {col}")
