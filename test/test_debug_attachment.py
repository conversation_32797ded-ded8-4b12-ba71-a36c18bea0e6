#!/usr/bin/env python3
"""
Test script to verify debugpy attachment capability.
"""

import subprocess
import time
import socket
import sys

def check_port_available(port):
    """Check if a port is available."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result != 0  # True if port is available (connection failed)
    except Exception:
        return False

def test_debugpy_installation():
    """Test if debugpy is properly installed."""
    print("Testing debugpy installation...")
    try:
        import debugpy
        print("✅ debugpy is installed")
        print(f"   Version: {debugpy.__version__}")
        return True
    except ImportError:
        print("❌ debugpy is not installed")
        print("   Install with: pip3 install debugpy")
        return False

def test_debug_script():
    """Test the debug script functionality."""
    print("\n" + "=" * 50)
    print("DEBUG SCRIPT TEST")
    print("=" * 50)
    
    # Test help option
    print("Testing --help option...")
    try:
        result = subprocess.run(['./debug.sh', '--help'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0 and '--attach' in result.stdout:
            print("✅ --help works and shows --attach option")
        else:
            print("❌ --help test failed")
            return False
    except Exception as e:
        print(f"❌ Error testing --help: {e}")
        return False
    
    # Check if ports are available
    print("\nChecking port availability...")
    if not check_port_available(5001):
        print("⚠️  Port 5001 is in use (Flask port)")
    else:
        print("✅ Port 5001 is available")
    
    if not check_port_available(5678):
        print("⚠️  Port 5678 is in use (debugpy port)")
    else:
        print("✅ Port 5678 is available")
    
    return True

def show_debug_instructions():
    """Show step-by-step debug instructions."""
    print("\n" + "=" * 50)
    print("VS CODE DEBUG INSTRUCTIONS")
    print("=" * 50)
    
    print("Now you can debug with breakpoints:")
    print()
    print("1. Start the debug server with attachment:")
    print("   ./debug.sh --attach")
    print()
    print("2. In VS Code:")
    print("   • Set breakpoints (click left of line numbers)")
    print("   • Press Ctrl+Shift+D (Run and Debug)")
    print("   • Select 'Attach to Flask Debug Server'")
    print("   • Press F5 to attach")
    print()
    print("3. Test your application:")
    print("   • Open http://localhost:5001")
    print("   • Upload sample_measuring_points.xlsx")
    print("   • Debugger will stop at your breakpoints!")
    print()
    print("Debug Controls:")
    print("   • F5: Continue")
    print("   • F10: Step over")
    print("   • F11: Step into")
    print("   • Shift+F11: Step out")
    print("   • Shift+F5: Stop debugging")
    print()
    print("Good breakpoint locations:")
    print("   • app.py line 67 (upload_file function)")
    print("   • sap_utils.py line 158 (SAP RFC call)")
    print("   • parser.py line 85 (_process_data method)")

def main():
    """Main test function."""
    print("Testing VS Code debug attachment setup...")
    print()
    
    # Test debugpy installation
    if not test_debugpy_installation():
        return
    
    # Test debug script
    if not test_debug_script():
        return
    
    # Show instructions
    show_debug_instructions()
    
    print("\n🎉 Debug attachment setup is ready!")
    print("\nNext step: Run './debug.sh --attach' and then attach VS Code debugger")

if __name__ == '__main__':
    main()
