#!/usr/bin/env python3
"""
Test script to verify measuring point formats for SAP RFC calls.
"""

import sys
import os

def test_measuring_point_formats():
    """Test different measuring point formats."""
    print("=" * 60)
    print("MEASURING POINT FORMAT TEST")
    print("=" * 60)
    
    # Test different measuring point formats
    test_cases = [
        {"meas_point": "61", "hours": 65024.0, "description": "Short number"},
        {"meas_point": "1000152", "hours": 65024.0, "description": "7-digit number"},
        {"meas_point": "000061", "hours": 65024.0, "description": "Zero-padded"},
        {"meas_point": "MP-61", "hours": 65024.0, "description": "With prefix"},
        {"meas_point": "61.0", "hours": 65024.0, "description": "With decimal"},
    ]
    
    print("Testing different measuring point formats:")
    print()
    
    for test_case in test_cases:
        meas_point = test_case["meas_point"]
        hours = test_case["hours"]
        description = test_case["description"]
        
        print(f"Testing {description}:")
        print(f"  Measuring Point: '{meas_point}' ({type(meas_point).__name__})")
        print(f"  Hours: {hours} ({type(hours).__name__})")
        
        # Test the formatting logic
        if isinstance(hours, (int, float)):
            if float(hours).is_integer():
                hours_str = str(int(float(hours)))
            else:
                hours_str = f"{float(hours):.10f}".rstrip('0').rstrip('.')
        else:
            hours_str = str(hours)
        
        print(f"  Formatted Hours: '{hours_str}'")
        print(f"  Would call: MEASUREM_DOCUM_RFC_SINGLE_001(MEASUREMENT_POINT='{meas_point}', RECORDED_VALUE='{hours_str}')")
        print()

def test_with_mock_sap():
    """Test with mock SAP to see if formatting works."""
    print("=" * 60)
    print("MOCK SAP TEST")
    print("=" * 60)
    
    try:
        from sap_utils_mock import MockSAPRFCClient, get_sap_connection_params
        
        connection_params = get_sap_connection_params()
        mock_client = MockSAPRFCClient(connection_params)
        
        # Connect
        connected, msg = mock_client.connect()
        if not connected:
            print(f"❌ Mock connection failed: {msg}")
            return
        
        print("✅ Mock SAP connected successfully")
        
        # Test the problematic case
        test_case = {"meas_point": "61", "hours": 65024.0}
        
        print(f"\nTesting problematic case:")
        print(f"  Measuring Point: '{test_case['meas_point']}'")
        print(f"  Hours: {test_case['hours']}")
        
        success, message, doc = mock_client.upload_measuring_point(
            test_case["meas_point"], 
            test_case["hours"]
        )
        
        if success:
            print(f"  ✅ Mock Success: {message}")
            print(f"  📄 Document: {doc}")
        else:
            print(f"  ❌ Mock Failed: {message}")
        
        mock_client.disconnect()
        
    except Exception as e:
        print(f"❌ Mock test failed: {e}")

def suggest_solutions():
    """Suggest potential solutions for the decimal conversion error."""
    print("=" * 60)
    print("POTENTIAL SOLUTIONS")
    print("=" * 60)
    
    print("The decimal.ConversionSyntax error suggests SAP is having trouble")
    print("parsing either the measuring point or the hours value.")
    print()
    print("Possible causes and solutions:")
    print()
    print("1. MEASURING POINT FORMAT:")
    print("   - SAP might expect a specific format (e.g., zero-padded)")
    print("   - Try: '000061' instead of '61'")
    print("   - Check if measuring point exists in SAP system")
    print()
    print("2. HOURS VALUE FORMAT:")
    print("   - SAP might expect specific decimal separator")
    print("   - Current format: '65024' (integer)")
    print("   - Try: '65024.00' (with decimals)")
    print()
    print("3. PARAMETER TYPES:")
    print("   - Both parameters might need to be strings")
    print("   - Ensure no special characters or spaces")
    print()
    print("4. SAP SYSTEM CONFIGURATION:")
    print("   - Check if measuring point '61' exists")
    print("   - Verify user has authorization for this measuring point")
    print("   - Check if measuring point is active/unlocked")
    print()
    print("Next steps:")
    print("1. Verify measuring point '61' exists in SAP")
    print("2. Try with a known valid measuring point")
    print("3. Check SAP transaction IW31/IW32 for measuring point details")

def main():
    """Main test function."""
    test_measuring_point_formats()
    test_with_mock_sap()
    suggest_solutions()

if __name__ == '__main__':
    main()
