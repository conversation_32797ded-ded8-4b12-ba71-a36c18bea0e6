#!/usr/bin/env python3
"""
Test script to verify VS Code debugging setup.
"""

import os
import json

def test_vscode_debug_setup():
    """Test VS Code debugging configuration."""
    print("=" * 60)
    print("VS CODE DEBUG SETUP TEST")
    print("=" * 60)
    
    # Check if .vscode directory exists
    if not os.path.exists('.vscode'):
        print("❌ .vscode directory not found")
        return False
    
    print("✅ .vscode directory found")
    
    # Check launch.json
    launch_json_path = '.vscode/launch.json'
    if not os.path.exists(launch_json_path):
        print("❌ launch.json not found")
        return False
    
    print("✅ launch.json found")
    
    # Parse launch.json
    try:
        with open(launch_json_path, 'r') as f:
            launch_config = json.load(f)
        
        configurations = launch_config.get('configurations', [])
        print(f"✅ Found {len(configurations)} debug configurations:")
        
        for i, config in enumerate(configurations, 1):
            name = config.get('name', 'Unnamed')
            config_type = config.get('type', 'Unknown')
            print(f"  {i}. {name} ({config_type})")
            
            # Check if it has proper Flask settings
            if 'flask' in name.lower():
                env = config.get('env', {})
                if 'FLASK_DEBUG' in env:
                    print(f"     ✅ FLASK_DEBUG: {env['FLASK_DEBUG']}")
                else:
                    print(f"     ⚠️  FLASK_DEBUG not set")
                
                if 'PORT' in env:
                    print(f"     ✅ PORT: {env['PORT']}")
                
                if 'envFile' in config:
                    print(f"     ✅ Environment file: {config['envFile']}")
        
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing launch.json: {e}")
        return False
    
    # Check tasks.json
    tasks_json_path = '.vscode/tasks.json'
    if os.path.exists(tasks_json_path):
        print("✅ tasks.json found")
        
        try:
            with open(tasks_json_path, 'r') as f:
                tasks_config = json.load(f)
            
            tasks = tasks_config.get('tasks', [])
            print(f"✅ Found {len(tasks)} VS Code tasks:")
            
            for i, task in enumerate(tasks, 1):
                label = task.get('label', 'Unnamed')
                print(f"  {i}. {label}")
                
        except json.JSONDecodeError as e:
            print(f"⚠️  Error parsing tasks.json: {e}")
    else:
        print("⚠️  tasks.json not found (optional)")
    
    # Check .env file
    env_file_path = '.env'
    if os.path.exists(env_file_path):
        print("✅ .env file found")
        
        # Count environment variables
        env_vars = []
        with open(env_file_path, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    var_name = line.split('=')[0]
                    env_vars.append(var_name)
        
        print(f"✅ Found {len(env_vars)} environment variables:")
        for var in env_vars:
            print(f"  - {var}")
    else:
        print("⚠️  .env file not found")
        print("   Run: python3 create_env_for_vscode.py")
    
    # Check sap_env.sh
    if os.path.exists('sap_env.sh'):
        print("✅ sap_env.sh found")
    else:
        print("⚠️  sap_env.sh not found")
        print("   Run: python3 setup_sap_connection.py")
    
    print()
    return True

def test_debug_instructions():
    """Provide debug instructions."""
    print("=" * 60)
    print("VS CODE DEBUG INSTRUCTIONS")
    print("=" * 60)
    
    print("To debug in VS Code:")
    print()
    print("1. Open VS Code in this directory")
    print("2. Press Ctrl+Shift+D (or Cmd+Shift+D on Mac)")
    print("3. Select one of these configurations:")
    print("   • 'Python Debugger: Flask' - Direct app.py execution")
    print("   • 'Flask Debug with SAP Environment' - With .env file")
    print("   • 'Flask Module Debug' - Using Flask module")
    print("4. Press F5 to start debugging")
    print()
    print("Debug features:")
    print("✅ Set breakpoints by clicking left of line numbers")
    print("✅ Step through code with F10 (step over) and F11 (step into)")
    print("✅ View variables in the Variables panel")
    print("✅ Use Debug Console for interactive debugging")
    print("✅ Auto-reload when code changes (in some configurations)")
    print()
    print("Troubleshooting:")
    print("• If SAP connection fails, check .env file has correct values")
    print("• If port is busy, change PORT in .env file")
    print("• If modules not found, install: pip3 install flask pandas openpyxl")
    print()

def main():
    """Main test function."""
    success = test_vscode_debug_setup()
    test_debug_instructions()
    
    if success:
        print("🎉 VS Code debugging setup is ready!")
        print()
        print("Quick start:")
        print("1. Open VS Code: code .")
        print("2. Press F5 to start debugging")
        print("3. Open http://localhost:5001 in browser")
    else:
        print("❌ VS Code debugging setup needs attention")

if __name__ == '__main__':
    main()
