#!/usr/bin/env python3
"""
Test script to verify RFC trace extraction works.
"""

import os
import re

def create_mock_trace_file():
    """Create a mock RFC trace file for testing."""
    mock_trace_content = """
RFC Trace Log
=============

Call to MEASUREM_DOCUM_RFC_SINGLE_001
Parameters:
  MEASUREMENT_POINT = 000000000061
  RECORDED_VALUE = 65026

Response:
  MEASUREMENT_DOCUMENT = 00000000000000000280
  RETURN_CODE = 0
  MESSAGE = Success

End of call
"""
    
    with open('test_dev_rfc.log', 'w') as f:
        f.write(mock_trace_content)
    
    print("✅ Created mock trace file: test_dev_rfc.log")

def test_extraction_patterns():
    """Test the extraction patterns."""
    print("=" * 60)
    print("TESTING RFC TRACE EXTRACTION PATTERNS")
    print("=" * 60)
    
    # Test content similar to what we see in real traces
    test_content = """
MEASUREMENT_DOCUMENT = 00000000000000000280
RETURN_CODE = 0
MESSAGE = Success
"""
    
    patterns = [
        r'MEASUREMENT_DOCUMENT.*?(\d{20})',
        r'MEASUREMENT_DOCUMENT.*?Value:\s*(\d+)',
        r'MEASUREMENT_DOCUMENT.*?=\s*(\d+)',
        r'Value:\s*(\d{15,})',
    ]
    
    print("Test content:")
    print(test_content)
    print()
    
    for i, pattern in enumerate(patterns, 1):
        print(f"Pattern {i}: {pattern}")
        matches = re.findall(pattern, test_content)
        if matches:
            raw_doc = matches[-1]
            clean_doc = raw_doc.lstrip('0') or '0'
            print(f"  ✅ Match found: '{raw_doc}' -> cleaned: '{clean_doc}'")
        else:
            print(f"  ❌ No match")
        print()

def test_real_trace_extraction():
    """Test extraction from real trace file if it exists."""
    print("=" * 60)
    print("TESTING REAL TRACE FILE EXTRACTION")
    print("=" * 60)
    
    trace_files = ['dev_rfc.log', 'dev_rfc.trc', 'rfc_trace.log', 'test_dev_rfc.log']
    
    for trace_file in trace_files:
        if os.path.exists(trace_file):
            print(f"Found trace file: {trace_file}")
            
            try:
                with open(trace_file, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                print(f"File size: {len(content)} characters")
                
                # Look for measurement document patterns
                patterns = [
                    r'MEASUREMENT_DOCUMENT.*?(\d{20})',
                    r'MEASUREMENT_DOCUMENT.*?=\s*(\d+)',
                    r'(\d{20})',  # Any 20-digit number
                ]
                
                found_any = False
                for pattern in patterns:
                    matches = re.findall(pattern, content)
                    if matches:
                        print(f"  Pattern '{pattern}' found {len(matches)} matches:")
                        for match in matches[-3:]:  # Show last 3 matches
                            clean_match = match.lstrip('0') or '0'
                            print(f"    '{match}' -> '{clean_match}'")
                        found_any = True
                
                if not found_any:
                    print(f"  ❌ No measurement document patterns found")
                    # Show a sample of the content
                    lines = content.split('\n')[:10]
                    print(f"  Sample content (first 10 lines):")
                    for line in lines:
                        print(f"    {line}")
                
            except Exception as e:
                print(f"  ❌ Error reading {trace_file}: {e}")
            
            print()
        else:
            print(f"Trace file not found: {trace_file}")

def test_sap_utils_extraction():
    """Test the extraction method from sap_utils."""
    print("=" * 60)
    print("TESTING SAP_UTILS EXTRACTION METHOD")
    print("=" * 60)
    
    try:
        # Create a mock SAP client to test the extraction method
        from sap_utils import SAPRFCClient
        
        # Create a dummy client (we won't connect)
        dummy_params = {
            'ashost': 'dummy',
            'sysnr': '00',
            'client': '100',
            'user': 'dummy',
            'passwd': 'dummy'
        }
        
        client = SAPRFCClient(dummy_params)
        
        # Test the extraction method
        result = client._extract_from_rfc_trace()
        
        if result:
            print(f"✅ Extraction successful: '{result}'")
        else:
            print("❌ No measurement document extracted")
            
    except Exception as e:
        print(f"❌ Error testing extraction method: {e}")

def cleanup():
    """Clean up test files."""
    test_files = ['test_dev_rfc.log']
    for file in test_files:
        if os.path.exists(file):
            os.remove(file)
            print(f"🗑️  Cleaned up: {file}")

def main():
    """Main test function."""
    print("Testing RFC Trace Extraction for Measurement Documents")
    print("This verifies we can extract the real measurement document from RFC traces")
    print()
    
    create_mock_trace_file()
    test_extraction_patterns()
    test_real_trace_extraction()
    test_sap_utils_extraction()
    
    print("=" * 60)
    print("NEXT STEPS")
    print("=" * 60)
    print("1. Enable RFC tracing: export RFC_TRACE=1")
    print("2. Run the application: ./debug.sh --attach")
    print("3. Upload a file to generate trace data")
    print("4. Check if the real measurement document is extracted")
    print()
    print("Expected result:")
    print("  Instead of 'PYRFC_ERROR_BUT_SUCCESS'")
    print("  You should see the real document number like '280'")
    
    cleanup()

if __name__ == '__main__':
    main()
