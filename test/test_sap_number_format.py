#!/usr/bin/env python3
"""
Test script to verify SAP-compatible number formatting.
"""

def test_sap_number_formatting():
    """Test the SAP number formatting logic."""
    print("=" * 60)
    print("SAP NUMBER FORMATTING TEST")
    print("=" * 60)
    
    test_cases = [
        65024.0,    # Float that's actually an integer
        66038,      # Integer
        45678.5,    # Float with decimal
        0.0,        # Zero as float
        999999.99,  # Float with two decimals
        123.100,    # Float with trailing zero
        456.000,    # Float with multiple trailing zeros
        789.123456, # Float with many decimals
    ]
    
    print("Testing number formatting for SAP RFC:")
    print()
    
    for hours in test_cases:
        # Apply the same logic as in sap_utils.py
        if isinstance(hours, (int, float)):
            if float(hours).is_integer():
                hours_str = str(int(float(hours)))  # Convert 65024.0 -> "65024"
            else:
                # For decimals, format without unnecessary trailing zeros but avoid scientific notation
                hours_str = f"{float(hours):.10f}".rstrip('0').rstrip('.')
        else:
            hours_str = str(hours)
        
        print(f"Input: {hours:>12} ({type(hours).__name__:>5}) -> SAP format: '{hours_str}'")
    
    print()
    print("✅ Number formatting test completed")
    print()
    print("Key improvements:")
    print("- Whole numbers: 65024.0 -> '65024' (no decimal point)")
    print("- Decimals: 123.100 -> '123.1' (trailing zeros removed)")
    print("- General format: Uses most compact representation")

def test_decimal_conversion_syntax():
    """Test potential decimal conversion issues."""
    print("\n" + "=" * 60)
    print("DECIMAL CONVERSION SYNTAX TEST")
    print("=" * 60)
    
    # Test values that might cause decimal conversion issues
    problematic_values = [
        "65024.0",    # String with decimal
        "65024",      # String without decimal
        "65024.00",   # String with trailing zeros
        "65,024.0",   # String with comma (locale issue)
        "65 024.0",   # String with space
        "65024.",     # String ending with decimal point
    ]
    
    print("Testing potential problematic string formats:")
    print()
    
    for value_str in problematic_values:
        try:
            # Try to convert to float and back to clean format
            float_val = float(value_str.replace(',', '').replace(' ', ''))
            if float_val.is_integer():
                clean_str = str(int(float_val))
            else:
                clean_str = f"{float_val:.10f}".rstrip('0').rstrip('.')
            
            print(f"'{value_str}' -> {float_val} -> '{clean_str}' ✅")
        except ValueError as e:
            print(f"'{value_str}' -> ERROR: {e} ❌")
    
    print()
    print("✅ Decimal conversion test completed")

if __name__ == '__main__':
    test_sap_number_formatting()
    test_decimal_conversion_syntax()
