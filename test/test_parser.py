"""
Test script for the Excel parser functionality.
"""

from parser import parse_measuring_points_file
import os

def test_parser():
    """Test the Excel parser with the sample file."""
    
    sample_file = 'sample_measuring_points.xlsx'
    
    if not os.path.exists(sample_file):
        print(f"Error: Sample file {sample_file} not found. Run create_sample_excel.py first.")
        return
    
    print("Testing Excel parser...")
    print(f"Parsing file: {sample_file}")
    print("-" * 50)
    
    # Parse the file
    records, errors = parse_measuring_points_file(sample_file)
    
    # Display results
    if errors:
        print("PARSING ERRORS:")
        for error in errors:
            print(f"  - {error}")
        print()
    
    if records:
        print(f"SUCCESSFULLY PARSED {len(records)} RECORDS:")
        print()
        
        for i, record in enumerate(records, 1):
            print(f"Record {i}:")
            print(f"  Row Number: {record['row_number']}")
            print(f"  Meas-Point: {record['meas_point']}")
            print(f"  Hours: {record['hours']}")
            print(f"  Manufacturer: {record['manufacturer']}")
            print(f"  Model: {record['model']}")
            print(f"  Unit ID: {record['unit_id']}")
            print(f"  Department: {record['department']}")
            print()
    else:
        print("No records were parsed successfully.")
    
    print("-" * 50)
    print(f"Summary: {len(records)} records parsed, {len(errors)} errors")

if __name__ == '__main__':
    test_parser()
