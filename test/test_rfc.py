import os
os.environ["RFC_NO_FLOAT_CONV"] = "1"
os.environ["RFC_NO_DECIMAL_CONV"] = "1"


from pyrfc import Connection, ABAPApplicationError, ABAPRuntimeError, LogonError, CommunicationError
from pprint import pprint

# 🔐 Replace with your actual connection details
conn_params = {
    "user": "induser",
    "passwd": "indience",
    "ashost": "**************",
    "sysnr": "00",
    "client": "100",
    "lang": "EN"
}

try:
    print("🔗 Connecting to SAP...")
    os.environ["RFC_NO_FLOAT_CONV"] = "1"
    os.environ["RFC_NO_DECIMAL_CONV"] = "1"
    conn = Connection(**conn_params)

    print("📦 Calling function module: MEASUREM_DOCUM_RFC_SINGLE_001")
    result = conn.call("MEASUREM_DOCUM_RFC_SINGLE_001", MEASUREMENT_POINT='000000000061', RECORDED_VALUE='65029')

    print("✅ RFC call successful. Output:")
    pprint(result)

except (ABAPApplicationError, ABAPRuntimeError, CommunicationError, LogonError) as e:
    print("❌ SAP RFC error:")
    print(e)

except Exception as e:
    print("❌ General Python error:")
    print(type(e), e)