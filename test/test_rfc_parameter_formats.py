#!/usr/bin/env python3
"""
Test script to verify different RFC parameter formats.
"""

def test_parameter_formats():
    """Test different parameter format combinations."""
    print("=" * 60)
    print("RFC PARAMETER FORMAT TESTING")
    print("=" * 60)
    
    # Test the format generation logic
    hours = 65026.0
    meas_point = "61"
    
    from sap_utils import alphaIN
    
    # Test measuring point formatting
    meas_point_formatted = alphaIN(meas_point, 12)
    print(f"Measuring Point: '{meas_point}' -> '{meas_point_formatted}'")
    
    # Test hours formatting
    if isinstance(hours, (int, float)):
        if float(hours).is_integer():
            hours_str = str(int(float(hours)))
        else:
            hours_str = f"{float(hours):.10f}".rstrip('0').rstrip('.')
    else:
        hours_str = str(hours)
    
    # Generate format variations
    hours_formats = [
        hours_str,                           # Original format
        f"{hours_str}.0",                   # With decimal
        f"{hours_str}.00",                  # With two decimals
        hours_str.zfill(10),                # Zero-padded
        f"{hours_str}.0".zfill(12),         # Padded with decimal
    ]
    
    print(f"\nHours: {hours} -> Primary format: '{hours_str}'")
    print("Alternative formats:")
    for i, fmt in enumerate(hours_formats):
        print(f"  {i+1}. '{fmt}'")
    
    print(f"\nRFC Call Parameter Combinations:")
    print(f"MEASUREMENT_POINT will be: '{meas_point_formatted}'")
    print("RECORDED_VALUE will be tried in these formats:")
    
    for i, hour_format in enumerate(hours_formats[:3]):  # First 3 formats
        print(f"\nFormat {i+1}: '{hour_format}'")
        print(f"  Basic call: RECORDED_VALUE='{hour_format}'")
        print(f"  String call: RECORDED_VALUE='{str(hour_format)}'")

def test_decimal_conversion_scenarios():
    """Test scenarios that might cause decimal conversion issues."""
    print("\n" + "=" * 60)
    print("DECIMAL CONVERSION SCENARIOS")
    print("=" * 60)
    
    test_values = [
        65026,          # Integer
        65026.0,        # Float (whole number)
        65026.5,        # Float with decimal
        "65026",        # String integer
        "65026.0",      # String float
        "65026.00",     # String with trailing zeros
        "0000065026",   # Zero-padded string
    ]
    
    print("Testing values that might cause decimal conversion issues:")
    print()
    
    for value in test_values:
        print(f"Value: {repr(value)} (type: {type(value).__name__})")
        
        # Test our formatting logic
        try:
            if isinstance(value, (int, float)):
                if float(value).is_integer():
                    formatted = str(int(float(value)))
                else:
                    formatted = f"{float(value):.10f}".rstrip('0').rstrip('.')
            else:
                formatted = str(value)
            
            print(f"  ✅ Our formatting: '{formatted}'")
            
            # Test if this might cause decimal conversion issues
            try:
                from decimal import Decimal
                decimal_test = Decimal(formatted)
                print(f"  ✅ Decimal conversion: {decimal_test}")
            except Exception as e:
                print(f"  ❌ Decimal conversion failed: {e}")
                
        except Exception as e:
            print(f"  ❌ Our formatting failed: {e}")
        
        print()

def show_debugging_tips():
    """Show debugging tips for the RFC call."""
    print("=" * 60)
    print("DEBUGGING TIPS")
    print("=" * 60)
    
    print("To debug the RFC parameter issue:")
    print()
    print("1. Check SAP RFC function signature:")
    print("   - What data types does MEASUREM_DOCUM_RFC_SINGLE_001 expect?")
    print("   - Are there specific format requirements?")
    print()
    print("2. Test with SAP GUI or SE37:")
    print("   - Try calling the function manually with different formats")
    print("   - See which format works in SAP directly")
    print()
    print("3. Check PyRFC version and SAP NW RFC SDK:")
    print("   - Some versions have different decimal handling")
    print("   - Update to latest versions if possible")
    print()
    print("4. Enable PyRFC debug logging:")
    print("   - Set environment variable: export RFC_TRACE=1")
    print("   - Check dev_rfc.log for detailed RFC traces")
    print()
    print("5. Try with different parameter types:")
    print("   - Pure strings: '65026'")
    print("   - Decimal format: '65026.00'")
    print("   - Zero-padded: '0000065026'")

def main():
    """Main test function."""
    print("Testing RFC parameter formats to resolve decimal conversion error...")
    print("This helps identify which format SAP expects for the RECORDED_VALUE parameter.")
    print()
    
    test_parameter_formats()
    test_decimal_conversion_scenarios()
    show_debugging_tips()
    
    print("\n🎯 NEXT STEPS:")
    print("1. Test the updated sap_utils.py with multiple format attempts")
    print("2. Check the logs to see which format succeeds")
    print("3. If all formats fail, check SAP function documentation")
    print("4. Consider enabling RFC_TRACE=1 for detailed debugging")

if __name__ == '__main__':
    main()
