#!/usr/bin/env python3
"""
Test script to verify PyRFC installation and SAP connectivity.
"""

import sys
import os

def test_pyrfc_import():
    """Test if PyRFC can be imported successfully."""
    print("Testing PyRFC import...")
    try:
        from pyrfc import Connection, ABAPApplicationError, ABAPRuntimeError, LogonError, CommunicationError
        print("✅ PyRFC import successful!")
        return True
    except ImportError as e:
        print(f"❌ PyRFC import failed: {e}")
        return False

def test_sap_connection():
    """Test SAP connection with provided parameters."""
    try:
        from pyrfc import Connection, LogonError, CommunicationError
        
        # Get connection parameters from environment variables
        connection_params = {
            'ashost': os.getenv('SAP_ASHOST', ''),
            'sysnr': os.getenv('SAP_SYSNR', '00'),
            'client': os.getenv('SAP_CLIENT', '100'),
            'user': os.getenv('SAP_USER', ''),
            'passwd': os.getenv('SAP_PASSWD', ''),
            'lang': os.getenv('SAP_LANG', 'EN')
        }
        
        # Check if required parameters are provided
        required_params = ['ashost', 'user', 'passwd']
        missing_params = [param for param in required_params if not connection_params[param]]
        
        if missing_params:
            print(f"❌ Missing required SAP connection parameters: {missing_params}")
            print("Please set the following environment variables:")
            for param in missing_params:
                print(f"  export SAP_{param.upper()}=your_value")
            return False
        
        print("Testing SAP connection...")
        print(f"Connecting to: {connection_params['ashost']} (Client: {connection_params['client']})")
        
        # Attempt connection
        conn = Connection(**connection_params)
        print("✅ SAP connection successful!")
        
        # Test a simple RFC call
        try:
            result = conn.call('RFC_SYSTEM_INFO')
            system_info = result.get('RFCSI_EXPORT', {})
            print(f"✅ RFC test successful!")
            print(f"   System: {system_info.get('RFCHOST', 'Unknown')}")
            print(f"   Release: {system_info.get('RFCREL', 'Unknown')}")
        except Exception as e:
            print(f"⚠️  RFC test failed: {e}")
        
        conn.close()
        return True
        
    except LogonError as e:
        print(f"❌ SAP Logon Error: {e}")
        return False
    except CommunicationError as e:
        print(f"❌ SAP Communication Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("SAP RFC CONNECTIVITY TEST")
    print("=" * 60)
    
    # Test 1: PyRFC Import
    if not test_pyrfc_import():
        print("\n❌ PyRFC is not properly installed. Please check SAP RFC SDK installation.")
        sys.exit(1)
    
    print()
    
    # Test 2: SAP Connection
    if test_sap_connection():
        print("\n🎉 All tests passed! SAP RFC is ready for use.")
        
        # Update app.py to use real SAP connection
        print("\nTo use real SAP connection in the application:")
        print("1. Set your SAP environment variables")
        print("2. Restart the Flask application")
        print("3. The app will automatically use real SAP connection instead of mock")
        
    else:
        print("\n⚠️  SAP connection test failed.")
        print("The application will continue to use mock SAP functionality.")
        print("Please check your SAP connection parameters and network connectivity.")

if __name__ == '__main__':
    main()
