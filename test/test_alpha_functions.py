#!/usr/bin/env python3
"""
Test script to verify both alphaIN and alphaOUT functions.
"""

def test_alpha_functions():
    """Test both alphaIN and alphaOUT functions."""
    
    # Import the functions from both modules
    from sap_utils import alphaIN as real_alphaIN, alphaOUT as real_alphaOUT
    from sap_utils_mock import alphaIN as mock_alphaIN, alphaOUT as mock_alphaOUT
    
    print("=" * 60)
    print("ALPHA FUNCTIONS TEST")
    print("=" * 60)
    
    # Test cases for alphaOUT
    alphaout_test_cases = [
        {"input": "000000000061", "expected": "61", "description": "Leading zeros removal"},
        {"input": "000001000152", "expected": "1000152", "description": "Partial leading zeros"},
        {"input": "123456789012", "expected": "123456789012", "description": "No leading zeros"},
        {"input": "000000000000", "expected": "0", "description": "All zeros"},
        {"input": "0000000000", "expected": "0", "description": "All zeros (10 chars)"},
        {"input": "  000000061  ", "expected": "61", "description": "With whitespace"},
        {"input": "000DOC123456", "expected": "DOC123456", "description": "Alphanumeric"},
        {"input": "0", "expected": "0", "description": "Single zero"},
        {"input": "00000ABC000", "expected": "ABC000", "description": "Leading zeros only"},
    ]
    
    print("Testing alphaOUT function:")
    print()
    
    all_passed = True
    
    for i, test_case in enumerate(alphaout_test_cases, 1):
        input_val = test_case["input"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        print(f"Test {i}: {description}")
        print(f"  Input: '{input_val}'")
        print(f"  Expected: '{expected}'")
        
        # Test real implementation
        try:
            real_result = real_alphaOUT(input_val)
            real_passed = real_result == expected
            print(f"  Real SAP: '{real_result}' {'✅' if real_passed else '❌'}")
            if not real_passed:
                all_passed = False
        except Exception as e:
            print(f"  Real SAP: ERROR - {e} ❌")
            all_passed = False
        
        # Test mock implementation
        try:
            mock_result = mock_alphaOUT(input_val)
            mock_passed = mock_result == expected
            print(f"  Mock SAP: '{mock_result}' {'✅' if mock_passed else '❌'}")
            if not mock_passed:
                all_passed = False
        except Exception as e:
            print(f"  Mock SAP: ERROR - {e} ❌")
            all_passed = False
        
        print()
    
    return all_passed

def test_roundtrip_conversion():
    """Test that alphaIN and alphaOUT are inverse operations."""
    print("=" * 60)
    print("ROUNDTRIP CONVERSION TEST")
    print("=" * 60)
    
    from sap_utils import alphaIN, alphaOUT
    
    test_values = [
        "61",
        "1000152",
        "0",
        "123456789012",
        "ABC123",
    ]
    
    print("Testing alphaIN -> alphaOUT roundtrip:")
    print()
    
    all_passed = True
    
    for value in test_values:
        # Apply alphaIN then alphaOUT
        padded = alphaIN(value, 12)
        trimmed = alphaOUT(padded)
        
        passed = trimmed == value
        print(f"'{value}' -> alphaIN(12) -> '{padded}' -> alphaOUT -> '{trimmed}' {'✅' if passed else '❌'}")
        
        if not passed:
            all_passed = False
    
    print()
    return all_passed

def test_measurement_document_processing():
    """Test measurement document processing with alphaOUT."""
    print("=" * 60)
    print("MEASUREMENT DOCUMENT PROCESSING TEST")
    print("=" * 60)
    
    from sap_utils import alphaOUT
    
    # Simulate SAP measurement documents (often have leading zeros)
    test_documents = [
        "0000001234567",
        "0000000000123",
        "000DOC1234567",
        "1234567890123",
        "0000000000000",
    ]
    
    print("Processing measurement documents:")
    print()
    
    for doc in test_documents:
        trimmed = alphaOUT(doc)
        print(f"Raw document: '{doc}' -> Trimmed: '{trimmed}'")
    
    print()

def test_with_mock_sap():
    """Test the complete flow with mock SAP to see alphaOUT in action."""
    print("=" * 60)
    print("MOCK SAP TEST WITH ALPHA FUNCTIONS")
    print("=" * 60)
    
    try:
        from sap_utils_mock import MockSAPRFCClient, get_sap_connection_params
        
        connection_params = get_sap_connection_params()
        mock_client = MockSAPRFCClient(connection_params)
        
        # Connect
        connected, msg = mock_client.connect()
        if not connected:
            print(f"❌ Mock connection failed: {msg}")
            return False
        
        print("✅ Mock SAP connected successfully")
        
        # Test with measuring point that will be formatted
        test_case = {"meas_point": "61", "hours": 65024.0}
        
        print(f"\nTesting: Measuring Point '{test_case['meas_point']}' with {test_case['hours']} hours")
        
        success, message, doc = mock_client.upload_measuring_point(
            test_case["meas_point"], 
            test_case["hours"]
        )
        
        if success:
            print(f"  ✅ Success: {message}")
            print(f"  📄 Document (trimmed): '{doc}'")
            print(f"  Note: Document was generated with leading zeros and trimmed using alphaOUT")
        else:
            print(f"  ❌ Failed: {message}")
        
        mock_client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Mock test failed: {e}")
        return False

def main():
    """Main test function."""
    print("Testing alphaIN and alphaOUT functions...")
    print("These functions handle SAP Alpha conversion for internal/external formats.")
    print()
    
    # Test alphaOUT function
    alphaout_passed = test_alpha_functions()
    
    # Test roundtrip conversion
    roundtrip_passed = test_roundtrip_conversion()
    
    # Test measurement document processing
    test_measurement_document_processing()
    
    # Test with mock SAP
    mock_passed = test_with_mock_sap()
    
    print("=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if alphaout_passed and roundtrip_passed and mock_passed:
        print("🎉 All tests passed!")
        print()
        print("Alpha functions working correctly:")
        print("  alphaIN('61', 12)  -> '000000000061' (pad with zeros)")
        print("  alphaOUT('000000000061') -> '61' (trim leading zeros)")
        print()
        print("Measurement documents will now be properly formatted:")
        print("  Raw SAP response: '0000001234567'")
        print("  Trimmed for display: '1234567'")
    else:
        print("❌ Some tests failed. Please check the output above.")

if __name__ == '__main__':
    main()
