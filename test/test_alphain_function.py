#!/usr/bin/env python3
"""
Test script to verify the alphaIN function for SAP measuring point formatting.
"""

def test_alphain_function():
    """Test the alphaIN function with various inputs."""
    
    # Import the function from both modules
    from sap_utils import alphaIN as real_alphaIN
    from sap_utils_mock import alphaIN as mock_alphaIN
    
    print("=" * 60)
    print("ALPHAIN FUNCTION TEST")
    print("=" * 60)
    
    test_cases = [
        {"input": "61", "length": 12, "expected": "000000000061", "description": "Short number"},
        {"input": "1000152", "length": 12, "expected": "000001000152", "description": "7-digit number"},
        {"input": "123456789012", "length": 12, "expected": "123456789012", "description": "Full 12 digits"},
        {"input": "0", "length": 12, "expected": "000000000000", "description": "Zero"},
        {"input": "  61  ", "length": 12, "expected": "000000000061", "description": "With whitespace"},
        {"input": 61, "length": 12, "expected": "000000000061", "description": "Integer input"},
        {"input": "61", "length": 10, "expected": "0000000061", "description": "Different length"},
        {"input": "ABCD123", "length": 12, "expected": "00000ABCD123", "description": "Alphanumeric"},
    ]
    
    print("Testing alphaIN function:")
    print()
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        input_val = test_case["input"]
        length = test_case["length"]
        expected = test_case["expected"]
        description = test_case["description"]
        
        print(f"Test {i}: {description}")
        print(f"  Input: {repr(input_val)} (length: {length})")
        print(f"  Expected: '{expected}'")
        
        # Test real implementation
        try:
            real_result = real_alphaIN(input_val, length)
            real_passed = real_result == expected
            print(f"  Real SAP: '{real_result}' {'✅' if real_passed else '❌'}")
            if not real_passed:
                all_passed = False
        except Exception as e:
            print(f"  Real SAP: ERROR - {e} ❌")
            all_passed = False
        
        # Test mock implementation
        try:
            mock_result = mock_alphaIN(input_val, length)
            mock_passed = mock_result == expected
            print(f"  Mock SAP: '{mock_result}' {'✅' if mock_passed else '❌'}")
            if not mock_passed:
                all_passed = False
        except Exception as e:
            print(f"  Mock SAP: ERROR - {e} ❌")
            all_passed = False
        
        print()
    
    if all_passed:
        print("🎉 All alphaIN tests passed!")
    else:
        print("❌ Some alphaIN tests failed!")
    
    return all_passed

def test_measuring_point_conversion():
    """Test the complete measuring point conversion process."""
    print("=" * 60)
    print("MEASURING POINT CONVERSION TEST")
    print("=" * 60)
    
    from sap_utils import alphaIN
    
    # Test cases based on the original problem
    test_cases = [
        "61",           # Original problematic case
        "1000152",      # From sample data
        "1000153",      # From sample data
        "0",            # Edge case
        "999999999999", # Maximum 12 digits
    ]
    
    print("Converting measuring points to SAP internal format:")
    print()
    
    for meas_point in test_cases:
        formatted = alphaIN(meas_point, 12)
        print(f"'{meas_point}' -> '{formatted}'")
        
        # Verify it's exactly 12 characters
        if len(formatted) == 12:
            print(f"  ✅ Length correct: {len(formatted)} characters")
        else:
            print(f"  ❌ Length incorrect: {len(formatted)} characters (expected 12)")
        
        # Verify it's all digits (for numeric inputs)
        if meas_point.isdigit() and formatted.isdigit():
            print(f"  ✅ All digits preserved")
        elif not meas_point.isdigit():
            print(f"  ℹ️  Non-numeric input (expected)")
        else:
            print(f"  ❌ Digit format issue")
        
        print()

def test_with_mock_sap():
    """Test the complete flow with mock SAP."""
    print("=" * 60)
    print("COMPLETE FLOW TEST WITH MOCK SAP")
    print("=" * 60)
    
    try:
        from sap_utils_mock import MockSAPRFCClient, get_sap_connection_params
        
        connection_params = get_sap_connection_params()
        mock_client = MockSAPRFCClient(connection_params)
        
        # Connect
        connected, msg = mock_client.connect()
        if not connected:
            print(f"❌ Mock connection failed: {msg}")
            return False
        
        print("✅ Mock SAP connected successfully")
        
        # Test the original problematic case
        test_cases = [
            {"meas_point": "61", "hours": 65024.0},
            {"meas_point": "1000152", "hours": 66038.0},
        ]
        
        for test_case in test_cases:
            meas_point = test_case["meas_point"]
            hours = test_case["hours"]
            
            print(f"\nTesting: Measuring Point '{meas_point}' with {hours} hours")
            
            success, message, doc = mock_client.upload_measuring_point(meas_point, hours)
            
            if success:
                print(f"  ✅ Success: {message}")
                print(f"  📄 Document: {doc}")
            else:
                print(f"  ❌ Failed: {message}")
        
        mock_client.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Mock test failed: {e}")
        return False

def main():
    """Main test function."""
    print("Testing alphaIN function and measuring point conversion...")
    print("This addresses the SAP requirement for 12-character measuring points.")
    print()
    
    # Test alphaIN function
    alphain_passed = test_alphain_function()
    
    # Test measuring point conversion
    test_measuring_point_conversion()
    
    # Test with mock SAP
    mock_passed = test_with_mock_sap()
    
    print("=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if alphain_passed and mock_passed:
        print("🎉 All tests passed!")
        print()
        print("The alphaIN function correctly formats measuring points:")
        print("  '61' -> '000000000061' (12 characters)")
        print()
        print("This should resolve the decimal.ConversionSyntax error.")
        print("SAP now receives properly formatted measuring points.")
    else:
        print("❌ Some tests failed. Please check the output above.")

if __name__ == '__main__':
    main()
