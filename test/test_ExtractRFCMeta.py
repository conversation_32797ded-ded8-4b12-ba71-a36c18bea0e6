#!/usr/bin/env python3
import os
from pyrfc import Connection
from pyrfc import ABAPApplicationError, ABAPRuntimeError, LogonError, CommunicationError

# Optional: Enable RFC SDK tracing
# os.environ["RFC_TRACE"] = "3"
# os.environ["RFC_TRACE_DIR"] = "/tmp/sap_rfc_logs"

# Optional: Bypass decimal conversion (may not always work)
# os.environ["RFC_NO_DECIMAL_CONV"] = "1"

# Replace with your actual SAP connection settings
conn_params = {
    "user": "induser",
    "passwd": "indience",
    "ashost": "**************",
    "sysnr": "00",
    "client": "100",
    "lang": "EN",
    # "trace": "3",  # optional trace flag here as well
}

def print_structure(fields, prefix="  "):
    for field in fields:
        print(f"{prefix}{field.name}: type={field.data_type}, length={field.nuc_length}")

def main():
    try:
        print("🔗 Connecting to SAP...")
        conn = Connection(**conn_params)

        func_name = "MEASUREM_DOCUM_RFC_SINGLE_001"
        print(f"📦 Getting metadata for function module: {func_name}")
        func_desc = conn.get_function_description(func_name)

        # print("📄 Parameters:")
        # for i, param in enumerate(func_desc.parameters):
        #     print(f"\n▶ Param #{i + 1}:")
        #     print(f"Raw: {param}")

        struct_desc = func_desc.parameters[0]["COMPLETE_DOCUMENT"]  # Assuming COMPLETE_DOCUMENT is first
        print(f"\n📦 Structure: {struct_desc.name} ({len(struct_desc.fields)} fields)")
        for field in struct_desc.fields:
            print(f"• {field['name']:30} | type: {field['type']:<8} | len: {field['nuc_length']} | decimals: {field['decimals']}")

        struct_desc = func_desc.parameters[0]["LINEAR_DATA_EXP"]  # Assuming COMPLETE_DOCUMENT is first
        print(f"\n📦 Structure: {struct_desc.name} ({len(struct_desc.fields)} fields)")
        for field in struct_desc.fields:
            print(f"• {field['name']:30} | type: {field['type']:<8} | len: {field['nuc_length']} | decimals: {field['decimals']}")

    except (ABAPApplicationError, ABAPRuntimeError, CommunicationError, LogonError) as e:
        print("❌ SAP RFC error:", str(e))
    except Exception as ex:
        print("❌ General error:", str(ex))

if __name__ == "__main__":
    main()