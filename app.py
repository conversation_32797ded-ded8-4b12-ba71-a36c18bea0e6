"""
Flask web application for measuring point uploads to SAP ECC.
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import os
import sys
import tempfile
from werkzeug.utils import secure_filename
import logging
from datetime import datetime

from parser import parse_measuring_points_file
from validation_utils import validate_measurement_records

# Import SAP utilities - prioritize real SAP connection
try:
    from sap_utils import upload_measuring_points_batch, SAPRFCClient, get_sap_connection_params
    print("✅ Using real SAP RFC connection")
    SAP_MODE = "REAL"
except ImportError as e:
    from sap_utils_mock import upload_measuring_points_batch, MockSAPRFCClient as SAPRFCClient, get_sap_connection_params
    print(f"⚠️  Using mock SAP utilities: {e}")
    SAP_MODE = "MOCK"

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'dev-secret-key-change-in-production')

# Configuration
UPLOAD_FOLDER = tempfile.gettempdir()
ALLOWED_EXTENSIONS = {'xlsx', 'xls'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH


def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS


@app.route('/')
def index():
    """Main page with file upload form."""
    return render_template('index.html')


@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle file upload and processing."""
    if 'file' not in request.files:
        flash('No file selected', 'error')
        return redirect(url_for('index'))
    
    file = request.files['file']
    
    if file.filename == '':
        flash('No file selected', 'error')
        return redirect(url_for('index'))
    
    if not allowed_file(file.filename):
        flash('Invalid file type. Please upload an Excel file (.xlsx or .xls)', 'error')
        return redirect(url_for('index'))
    
    try:
        # Save uploaded file temporarily
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        temp_filename = f"{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], temp_filename)
        file.save(file_path)
        
        logger.info(f"File uploaded: {temp_filename}")
        
        # Parse the Excel file
        records, parse_errors = parse_measuring_points_file(file_path)
        
        # Clean up temporary file
        try:
            os.remove(file_path)
        except Exception as e:
            logger.warning(f"Could not remove temporary file {file_path}: {e}")
        
        # Check for parsing errors
        if parse_errors:
            error_msg = "Errors occurred while parsing the file:"
            for error in parse_errors:
                error_msg += f"\n• {error}"
            flash(error_msg, 'error')
            return redirect(url_for('index'))
        
        if not records:
            flash('No valid records found in the uploaded file', 'warning')
            return redirect(url_for('index'))

        # Phase 2: Validate records against SAP measurement history
        logger.info(f"Phase 2: Validating {len(records)} records against SAP measurement history")

        # Create SAP client for validation
        connection_params = get_sap_connection_params()
        sap_client = SAPRFCClient(connection_params)

        # Connect to SAP for validation
        connected, connect_msg = sap_client.connect()
        if not connected:
            flash(f'SAP connection failed: {connect_msg}', 'error')
            return redirect(url_for('index'))

        try:
            # Validate records
            valid_records, invalid_records = validate_measurement_records(records, sap_client)

            logger.info(f"Validation complete: {len(valid_records)} valid, {len(invalid_records)} invalid")

            # Upload only valid records to SAP
            upload_results = []

            if valid_records:
                logger.info(f"Uploading {len(valid_records)} valid records to SAP")
                upload_results = upload_measuring_points_batch(valid_records)

            # Add invalid records to results with validation errors
            for invalid_record in invalid_records:
                upload_results.append({
                    'record': invalid_record,
                    'success': False,
                    'message': f"Validation failed: {invalid_record.get('validation_error', 'Unknown validation error')}",
                    'measurement_document': None
                })

        finally:
            # Always disconnect
            if hasattr(sap_client, 'disconnect'):
                sap_client.disconnect()
        
        # Prepare results for display
        success_count = sum(1 for result in upload_results if result['success'])
        error_count = len(upload_results) - success_count
        
        flash(f'Processing complete: {success_count} successful, {error_count} failed', 
              'success' if error_count == 0 else 'warning')
        
        return render_template('results.html', 
                             results=upload_results,
                             success_count=success_count,
                             error_count=error_count,
                             filename=filename)
    
    except Exception as e:
        error_msg = f"An unexpected error occurred: {str(e)}"
        logger.error(error_msg)
        flash(error_msg, 'error')
        return redirect(url_for('index'))


@app.route('/api/upload', methods=['POST'])
def api_upload():
    """API endpoint for file upload (JSON response)."""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if not allowed_file(file.filename):
        return jsonify({'error': 'Invalid file type. Please upload an Excel file (.xlsx or .xls)'}), 400
    
    try:
        # Save uploaded file temporarily
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        temp_filename = f"{timestamp}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], temp_filename)
        file.save(file_path)
        
        # Parse the Excel file
        records, parse_errors = parse_measuring_points_file(file_path)
        
        # Clean up temporary file
        try:
            os.remove(file_path)
        except Exception as e:
            logger.warning(f"Could not remove temporary file {file_path}: {e}")
        
        # Check for parsing errors
        if parse_errors:
            return jsonify({
                'error': 'File parsing failed',
                'details': parse_errors
            }), 400
        
        if not records:
            return jsonify({'error': 'No valid records found in the uploaded file'}), 400

        # Phase 2: Validate records against SAP measurement history
        logger.info(f"API: Phase 2 - Validating {len(records)} records against SAP measurement history")

        # Create SAP client for validation
        connection_params = get_sap_connection_params()
        sap_client = SAPRFCClient(connection_params)

        # Connect to SAP for validation
        connected, connect_msg = sap_client.connect()
        if not connected:
            return jsonify({'error': f'SAP connection failed: {connect_msg}'}), 500

        try:
            # Validate records
            valid_records, invalid_records = validate_measurement_records(records, sap_client)

            logger.info(f"API: Validation complete - {len(valid_records)} valid, {len(invalid_records)} invalid")

            # Upload only valid records to SAP
            upload_results = []

            if valid_records:
                logger.info(f"API: Uploading {len(valid_records)} valid records to SAP")
                upload_results = upload_measuring_points_batch(valid_records)

            # Add invalid records to results with validation errors
            for invalid_record in invalid_records:
                upload_results.append({
                    'record': invalid_record,
                    'success': False,
                    'message': f"Validation failed: {invalid_record.get('validation_error', 'Unknown validation error')}",
                    'measurement_document': None
                })

        finally:
            # Always disconnect
            if hasattr(sap_client, 'disconnect'):
                sap_client.disconnect()
        
        # Prepare response
        success_count = sum(1 for result in upload_results if result['success'])
        error_count = len(upload_results) - success_count
        
        return jsonify({
            'success': True,
            'message': f'Processing complete: {success_count} successful, {error_count} failed',
            'results': upload_results,
            'summary': {
                'total_records': len(upload_results),
                'successful': success_count,
                'failed': error_count,
                'filename': filename
            }
        })
    
    except Exception as e:
        error_msg = f"An unexpected error occurred: {str(e)}"
        logger.error(error_msg)
        return jsonify({'error': error_msg}), 500


@app.route('/health')
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'sap_mode': SAP_MODE
    })


@app.route('/status')
def status_check():
    """Detailed status endpoint."""
    return jsonify({
        'application': 'Measuring Point Upload',
        'status': 'running',
        'sap_mode': SAP_MODE,
        'sap_description': 'Real SAP RFC connection' if SAP_MODE == 'REAL' else 'Mock SAP for testing',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    })


@app.errorhandler(413)
def too_large(e):
    """Handle file too large error."""
    flash('File too large. Maximum size is 16MB.', 'error')
    return redirect(url_for('index'))


@app.errorhandler(404)
def not_found(e):
    """Handle 404 errors."""
    return render_template('404.html'), 404


@app.errorhandler(500)
def internal_error(e):
    """Handle 500 errors."""
    logger.error(f"Internal server error: {str(e)}")
    return render_template('500.html'), 500


if __name__ == '__main__':
    # Create upload directory if it doesn't exist
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # Run the application
    debug_mode = os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
    port = int(os.environ.get('PORT', 5001))
    
    logger.info(f"Starting Flask application on port {port}")
    app.run(debug=debug_mode, host='0.0.0.0', port=port)
