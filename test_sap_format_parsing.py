#!/usr/bin/env python3
"""
Test script to verify SAP format parsing works correctly.
Tests the actual SAP response format parsing.
"""

import sys
import os
from datetime import datetime
from decimal import Decimal

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from validation_utils import MeasurementValidator


class TestSAPClient:
    """Test SAP client with actual SAP response format."""
    
    def __init__(self):
        self.connected = True
    
    def connect(self):
        return True, "Test connection successful"
    
    def disconnect(self):
        pass
    
    def get_measurement_history(self, meas_point: str):
        """Return actual SAP response format for testing."""
        # Simulate actual SAP response format
        measurement_data = {
            'MDOCM': '00000000000000000325',
            'IDATE': '20250721',  # YYYYMMDD format
            'READG': Decimal('65032.000000000000'),  # Decimal object
            'ITIME': '171743'  # HHMMSS format
        }
        return True, "Success", measurement_data


def test_actual_sap_format():
    """Test parsing of actual SAP response format."""
    print("=" * 60)
    print("TESTING ACTUAL SAP FORMAT PARSING")
    print("=" * 60)
    
    # Create test client with actual SAP format
    test_client = TestSAPClient()
    validator = MeasurementValidator(test_client)
    
    # Test record that should be valid
    test_record = {
        'row_number': 2,
        'meas_point': '1000',
        'hours': 65050.0,  # 18 hours more than SAP reading (65032)
        'last_entry_date': '7/22/25',  # Next day
        'last_entry_time': '12:00PM',  # 18+ hours later
        'manufacturer': 'KOMATSU'
    }
    
    print("Test record:")
    print(f"  Meas-Point: {test_record['meas_point']}")
    print(f"  Hours: {test_record['hours']}")
    print(f"  Last Entry Date: {test_record['last_entry_date']}")
    print(f"  Last Entry Time: {test_record['last_entry_time']}")
    
    # Get SAP data
    success, message, sap_data = test_client.get_measurement_history(test_record['meas_point'])
    print(f"\nSAP Response:")
    print(f"  MDOCM: {sap_data['MDOCM']}")
    print(f"  IDATE: {sap_data['IDATE']} (SAP format)")
    print(f"  READG: {sap_data['READG']} (type: {type(sap_data['READG'])})")
    print(f"  ITIME: {sap_data['ITIME']} (SAP format)")
    
    # Test validation
    is_valid, error_msg = validator.validate_measurement_record(test_record)
    
    print(f"\nValidation Result:")
    print(f"  Valid: {'✅' if is_valid else '❌'}")
    print(f"  Message: {error_msg}")
    
    # Test date/time parsing specifically
    print(f"\nDate/Time Parsing Test:")
    
    # Test SAP date/time parsing
    sap_datetime = validator._parse_sap_datetime(sap_data['IDATE'], sap_data['ITIME'])
    print(f"  SAP DateTime: {sap_datetime}")
    
    # Test Excel date/time parsing
    excel_datetime = validator._parse_excel_datetime(test_record['last_entry_date'], test_record['last_entry_time'])
    print(f"  Excel DateTime: {excel_datetime}")
    
    if sap_datetime and excel_datetime:
        time_diff = excel_datetime - sap_datetime
        time_diff_hours = time_diff.total_seconds() / 3600
        hours_diff = test_record['hours'] - float(sap_data['READG'])
        
        print(f"  Time Difference: {time_diff_hours:.2f} hours")
        print(f"  Hours Difference: {hours_diff:.2f} hours")
        print(f"  Validation Logic: {time_diff_hours:.2f} >= {hours_diff:.2f} = {'✅' if time_diff_hours >= hours_diff else '❌'}")


def test_edge_cases():
    """Test edge cases with SAP format."""
    print("\n" + "=" * 60)
    print("TESTING EDGE CASES")
    print("=" * 60)
    
    test_client = TestSAPClient()
    validator = MeasurementValidator(test_client)
    
    # Test various SAP date/time formats
    test_cases = [
        ("20250721", "171743", "2025-07-21 17:17:43"),
        ("20241231", "235959", "2024-12-31 23:59:59"),
        ("20250101", "000000", "2025-01-01 00:00:00"),
        ("20250630", "120000", "2025-06-30 12:00:00"),
    ]
    
    print("SAP Date/Time Format Tests:")
    for sap_date, sap_time, expected in test_cases:
        result = validator._parse_sap_datetime(sap_date, sap_time)
        expected_dt = datetime.strptime(expected, "%Y-%m-%d %H:%M:%S")
        
        if result and result == expected_dt:
            print(f"✅ {sap_date} + {sap_time} -> {result}")
        else:
            print(f"❌ {sap_date} + {sap_time} -> {result} (expected {expected_dt})")
    
    # Test Decimal handling
    print(f"\nDecimal Handling Tests:")
    decimal_values = [
        Decimal('65032.000000000000'),
        Decimal('0.000000000000'),
        Decimal('999999.999999999999'),
    ]
    
    for decimal_val in decimal_values:
        try:
            float_val = float(decimal_val)
            print(f"✅ Decimal({decimal_val}) -> float({float_val})")
        except Exception as e:
            print(f"❌ Decimal({decimal_val}) -> Error: {e}")


if __name__ == "__main__":
    print("SAP Format Parsing Test")
    print("=" * 60)
    
    try:
        test_actual_sap_format()
        test_edge_cases()
        
        print("\n" + "=" * 60)
        print("SAP FORMAT PARSING TESTS COMPLETED")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
