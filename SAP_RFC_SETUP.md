# SAP RFC Setup Guide

## Issue
PyRFC requires the SAP NetWeaver RFC SDK to be installed on your system. Without it, you'll get import errors like:
```
ImportError: cannot import name 'Connection' from 'pyrfc'
```

## Solution Options

### Option 1: Install SAP NetWeaver RFC SDK (Recommended for Production)

#### Step 1: Download SAP NW RFC SDK
1. Go to [SAP Support Portal](https://support.sap.com/en/product/connectors/nwrfcsdk.html)
2. Login with your SAP account (S-user)
3. Download the appropriate SDK for your platform:
   - **macOS**: `nwrfc750P_8-********.zip` (or latest version)
   - **Linux**: `nwrfc750P_8-********.zip`
   - **Windows**: `nwrfc750P_8-********.zip`

#### Step 2: Install the SDK
**For macOS/Linux:**
```bash
# Extract the SDK
unzip nwrfc750P_8-********.zip

# Move to standard location
sudo mkdir -p /usr/local/sap
sudo mv nwrfcsdk /usr/local/sap/

# Set environment variables (add to ~/.bashrc or ~/.zshrc)
export SAPNWRFC_HOME=/usr/local/sap/nwrfcsdk
export LD_LIBRARY_PATH=$SAPNWRFC_HOME/lib:$LD_LIBRARY_PATH
export PATH=$SAPNWRFC_HOME/bin:$PATH

# For macOS, also set:
export DYLD_LIBRARY_PATH=$SAPNWRFC_HOME/lib:$DYLD_LIBRARY_PATH
```

**For Windows:**
```cmd
# Extract to C:\nwrfcsdk
# Add to PATH environment variable:
C:\nwrfcsdk\lib
```

#### Step 3: Reinstall PyRFC
```bash
pip uninstall pyrfc
pip install pyrfc
```

#### Step 4: Test Installation
```python
from pyrfc import Connection
print("PyRFC installed successfully!")
```

### Option 2: Use Docker (Easier Setup)

Create a Docker container with SAP RFC SDK pre-installed:

```dockerfile
FROM python:3.9

# Install system dependencies
RUN apt-get update && apt-get install -y \
    wget \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Copy SAP NW RFC SDK (you need to download this separately)
COPY nwrfcsdk /usr/local/sap/nwrfcsdk

# Set environment variables
ENV SAPNWRFC_HOME=/usr/local/sap/nwrfcsdk
ENV LD_LIBRARY_PATH=$SAPNWRFC_HOME/lib:$LD_LIBRARY_PATH

# Install Python dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy application
COPY . /app
WORKDIR /app

EXPOSE 5000
CMD ["python", "app.py"]
```

### Option 3: Use Mock Implementation (Development/Testing)

For development and testing without SAP access, use the mock implementation:

```python
# In app.py, we already have this fallback:
try:
    from sap_utils import upload_measuring_points_batch
except ImportError:
    from sap_utils_mock import upload_measuring_points_batch
    print("Warning: Using mock SAP utilities for testing.")
```

### Option 4: Alternative SAP Connection Methods

If PyRFC is problematic, consider these alternatives:

1. **SAP REST APIs**: Use SAP's OData/REST services
2. **SAP Cloud Platform**: Use cloud-based integration
3. **SAP JCo (Java)**: Use Java Connector with Python bridge
4. **Custom RFC Server**: Create a middleware service

## Current Project Status

The application is currently configured to:
1. Try to import the real `sap_utils` module
2. Fall back to `sap_utils_mock` for testing if PyRFC is not available
3. Continue development and testing without SAP connectivity

## Next Steps

1. **For Development**: Continue using the mock implementation
2. **For Production**: Install SAP NW RFC SDK following Option 1
3. **For Team Development**: Consider Docker approach (Option 2)

## Environment Variables for SAP Connection

Set these environment variables for your SAP system:

```bash
export SAP_ASHOST=your-sap-server.company.com
export SAP_SYSNR=00
export SAP_CLIENT=100
export SAP_USER=your-username
export SAP_PASSWD=your-password
export SAP_LANG=EN
```

## Testing the Application

You can test the application now using the mock implementation:

```bash
python3 app.py
```

The mock will simulate SAP RFC calls with realistic responses and some random failures for testing.
