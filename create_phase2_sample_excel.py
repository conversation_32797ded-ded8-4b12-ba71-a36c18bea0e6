#!/usr/bin/env python3
"""
Create a sample Excel file for testing Phase 2 validation functionality.
This file includes the required columns for phase 2 validation.
"""

import pandas as pd
from datetime import datetime, timedelta
import os

def create_phase2_sample_excel():
    """Create a sample Excel file with phase 2 validation data."""
    
    # Create sample data with various scenarios
    base_date = datetime.now() - timedelta(days=1)
    
    data = [
        {
            'Meas-Point': '1000152',
            'Manufacturer': 'KOMATSU',
            'Model': '830E-1AC',
            'Unit ID': '201',
            'Hours': 65024,
            'Last Entry Date': base_date.strftime('%m/%d/%y'),
            'Last Entry Time': base_date.strftime('%I:%M%p'),
            'INVESTIGATE': '155',
            'DEPARTMENT': 'Mine Ops'
        },
        {
            'Meas-Point': '1000153',
            'Manufacturer': 'KOMATSU',
            'Model': '830E-1AC',
            'Unit ID': '202',
            'Hours': 66038,
            'Last Entry Date': (base_date + timedelta(hours=2)).strftime('%m/%d/%y'),
            'Last Entry Time': (base_date + timedelta(hours=2)).strftime('%I:%M%p'),
            'INVESTIGATE': '155',
            'DEPARTMENT': 'Mine Ops'
        },
        {
            'Meas-Point': '1000154',
            'Manufacturer': 'CATERPILLAR',
            'Model': '797F',
            'Unit ID': '203',
            'Hours': 45678,
            'Last Entry Date': (base_date + timedelta(hours=48)).strftime('%m/%d/%y'),
            'Last Entry Time': (base_date + timedelta(hours=48)).strftime('%I:%M%p'),
            'INVESTIGATE': '156',
            'DEPARTMENT': 'Mine Ops'
        },
        {
            'Meas-Point': '1000155',
            'Manufacturer': 'LIEBHERR',
            'Model': 'T282C',
            'Unit ID': '204',
            'Hours': 52345,
            'Last Entry Date': (base_date + timedelta(hours=10)).strftime('%m/%d/%y'),
            'Last Entry Time': (base_date + timedelta(hours=10)).strftime('%I:%M%p'),
            'INVESTIGATE': '157',
            'DEPARTMENT': 'Mine Ops'
        },
        {
            'Meas-Point': '1000156',
            'Manufacturer': 'KOMATSU',
            'Model': 'PC8000-6',
            'Unit ID': '205',
            'Hours': 38912,
            'Last Entry Date': (base_date + timedelta(hours=25)).strftime('%m/%d/%y'),
            'Last Entry Time': (base_date + timedelta(hours=25)).strftime('%I:%M%p'),
            'INVESTIGATE': '158',
            'DEPARTMENT': 'Mine Ops'
        }
    ]
    
    # Create DataFrame
    df = pd.DataFrame(data)
    
    # Save to Excel file
    filename = 'sample_measuring_points_phase2.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ Created sample Excel file: {filename}")
    print(f"📊 Contains {len(data)} sample records with phase 2 validation data")
    print("\nSample data preview:")
    print(df.to_string(index=False))
    
    return filename

if __name__ == "__main__":
    create_phase2_sample_excel()
