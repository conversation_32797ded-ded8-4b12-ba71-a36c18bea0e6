# Application Startup Guide

This guide explains the different ways to start the Measuring Point Upload application with SAP connectivity.

## 🚀 Quick Start Options

### Option 1: Simple Startup (Recommended)
```bash
./run.sh
```
- ✅ Loads SAP environment automatically
- ✅ Finds available port automatically
- ✅ Simple and fast
- ✅ Shows startup status

### Option 2: Full-Featured Startup
```bash
./start_app.sh
```
- ✅ Comprehensive startup checks
- ✅ Dependency validation
- ✅ Health checks
- ✅ Detailed logging
- ✅ Graceful shutdown handling
- ✅ Command line options

### Option 3: Manual Startup
```bash
# Load SAP environment
source ./sap_env.sh

# Start application
python3 app.py
```

### Option 4: Custom Port
```bash
# Using full-featured script
./start_app.sh -p 8080

# Using simple script
PORT=8080 ./run.sh

# Manual
PORT=8080 python3 app.py
```

## 📋 Prerequisites

### Required Files
- `app.py` - Main Flask application
- `sap_env.sh` - SAP environment variables (created by setup_sap_connection.py)
- `requirements.txt` - Python dependencies

### Optional Files
- `.env` - Environment variables file
- `config_example.py` - Configuration template

## 🔧 SAP Configuration

### First Time Setup
1. **Configure SAP Connection:**
   ```bash
   python3 setup_sap_connection.py
   ```
   This creates:
   - `sap_env.sh` - Shell environment script
   - `.env` - Environment variables file

2. **Test SAP Connection:**
   ```bash
   python3 test_pyrfc.py
   ```

3. **Start Application:**
   ```bash
   ./run.sh
   ```

### SAP Environment Variables
The `sap_env.sh` file contains:
```bash
export SAP_ASHOST="your-sap-server.com"
export SAP_SYSNR="00"
export SAP_CLIENT="100"
export SAP_USER="your-username"
export SAP_PASSWD="your-password"
export SAP_LANG="EN"
```

## 🖥️ Startup Script Features

### `run.sh` (Simple Script)
- **Auto-loads** SAP environment from `sap_env.sh`
- **Auto-detects** available port (starts from 5000)
- **Fallback** to mock SAP if no environment file
- **Quick startup** with minimal output

### `start_app.sh` (Full-Featured Script)
- **Comprehensive checks** for Python, dependencies, files
- **Health monitoring** with endpoint testing
- **Graceful shutdown** with cleanup
- **Command line options** (-h for help, -p for port)
- **Colored output** for better readability
- **Error handling** with detailed messages

## 📊 Application Status

### Check Application Status
```bash
# Health check
curl http://localhost:5001/health

# Detailed status
curl http://localhost:5001/status
```

### Status Response
```json
{
  "application": "Measuring Point Upload",
  "status": "running",
  "sap_mode": "REAL",
  "sap_description": "Real SAP RFC connection",
  "timestamp": "2025-07-17T22:00:00.000000",
  "version": "1.0.0",
  "python_version": "3.9.6"
}
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Port Already in Use
```bash
# Error: Port 5000 is in use
# Solution: Scripts automatically find next available port
Port 5000 is busy, trying 5001
```

#### 2. SAP Environment Not Loaded
```bash
# Warning: No sap_env.sh found
# Solution: Run setup script
python3 setup_sap_connection.py
```

#### 3. Missing Dependencies
```bash
# Error: Module not found
# Solution: Install requirements
pip3 install -r requirements.txt
```

#### 4. SAP Connection Failed
```bash
# Check SAP connectivity
python3 test_pyrfc.py

# Verify environment variables
source ./sap_env.sh
echo $SAP_ASHOST
```

### Log Locations
- **Application logs**: Console output
- **SAP RFC logs**: `dev_rfc.log` (if created)
- **Flask logs**: Console output with timestamps

## 🛑 Stopping the Application

### Graceful Shutdown
- **Ctrl+C** in terminal
- **Kill process** by PID
- Scripts handle cleanup automatically

### Force Stop
```bash
# Find process
ps aux | grep python3

# Kill by PID
kill <PID>

# Kill by port
lsof -ti:5001 | xargs kill
```

## 🔄 Development Workflow

### Typical Development Session
```bash
# 1. Configure SAP (first time only)
python3 setup_sap_connection.py

# 2. Start application
./run.sh

# 3. Test functionality
python3 test_complete_workflow.py

# 4. Make changes to code

# 5. Restart application (Ctrl+C, then ./run.sh)
```

### Testing Different Modes
```bash
# Test with real SAP
source ./sap_env.sh
python3 app.py

# Test with mock SAP
unset SAP_ASHOST SAP_USER SAP_PASSWD
python3 app.py
```

## 📱 Access Points

Once started, the application is available at:

- **Web Interface**: http://localhost:5001
- **API Upload**: http://localhost:5001/api/upload
- **Health Check**: http://localhost:5001/health
- **Status**: http://localhost:5001/status

## 🎯 Production Deployment

For production deployment:

1. **Use production WSGI server**:
   ```bash
   pip install gunicorn
   gunicorn -w 4 -b 0.0.0.0:5000 app:app
   ```

2. **Set production environment variables**
3. **Configure reverse proxy** (nginx/Apache)
4. **Set up monitoring** and logging
5. **Use systemd service** for auto-start

## 📞 Support

If you encounter issues:
1. Check this guide first
2. Run diagnostic scripts (`test_pyrfc.py`, `test_complete_workflow.py`)
3. Check application logs
4. Verify SAP connectivity
5. Review SAP RFC SDK installation
