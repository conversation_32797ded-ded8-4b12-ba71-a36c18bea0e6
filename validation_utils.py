"""
Validation utilities for measuring point data.
Handles validation logic for phase 2 feature including date/time and hours validation.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Tuple, Optional
import pandas as pd

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MeasurementValidator:
    """Validator for measuring point data against SAP measurement history."""
    
    def __init__(self, sap_client):
        """
        Initialize validator with SAP client.
        
        Args:
            sap_client: SAP RFC client instance (real or mock)
        """
        self.sap_client = sap_client
    
    def validate_measurement_record(self, record: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Validate a measurement record against SAP measurement history.
        
        Args:
            record: Dictionary containing measurement record data with keys:
                   - meas_point: Measuring point identifier
                   - hours (currHrs): Current hours reading
                   - last_entry_date (entryDate): Last entry date from Excel
                   - last_entry_time (entryTime): Last entry time from Excel
        
        Returns:
            Tuple of (is_valid, error_message)
            is_valid: True if validation passes, False otherwise
            error_message: Description of validation error if any
        """
        try:
            # Extract required fields
            meas_point = record.get('meas_point')
            curr_hrs = record.get('hours')
            entry_date = record.get('last_entry_date')
            entry_time = record.get('last_entry_time')
            
            # Check if all required fields are present
            if not all([meas_point, curr_hrs is not None, entry_date, entry_time]):
                missing_fields = []
                if not meas_point:
                    missing_fields.append('meas_point')
                if curr_hrs is None:
                    missing_fields.append('hours')
                if not entry_date:
                    missing_fields.append('last_entry_date')
                if not entry_time:
                    missing_fields.append('last_entry_time')
                
                return False, f"Missing required fields: {', '.join(missing_fields)}"
            
            # Get measurement history from SAP
            success, message, measurement_data = self.sap_client.get_measurement_history(meas_point)
            
            if not success:
                return False, f"Failed to retrieve measurement history: {message}"
            
            if not measurement_data:
                return False, "No measurement history data returned from SAP"
            
            # Extract SAP measurement data
            sap_date = measurement_data.get('IDATE')
            sap_time = measurement_data.get('ITIME', '00:00:00')
            sap_reading = measurement_data.get('READG')
            
            if not all([sap_date, sap_reading]):
                return False, "Incomplete measurement history data from SAP"
            
            # Parse and validate the data
            validation_result = self._validate_hours_against_history(
                curr_hrs, entry_date, entry_time, sap_date, sap_time, sap_reading
            )
            
            return validation_result
            
        except Exception as e:
            error_msg = f"Validation error: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def _validate_hours_against_history(self, curr_hrs: float, entry_date: str, entry_time: str,
                                      sap_date: str, sap_time: str, sap_reading: str) -> Tuple[bool, str]:
        """
        Validate current hours against SAP measurement history.
        
        Logic: Check if the time difference between Excel entry and SAP measurement
        is greater than or equal to the hours difference (currHrs - READG).
        
        Args:
            curr_hrs: Current hours from Excel
            entry_date: Entry date from Excel
            entry_time: Entry time from Excel
            sap_date: SAP measurement date (YYYY-MM-DD format)
            sap_time: SAP measurement time (HH:MM:SS format)
            sap_reading: SAP reading value
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Parse Excel entry date and time
            excel_datetime = self._parse_excel_datetime(entry_date, entry_time)
            if not excel_datetime:
                return False, "Invalid date/time format in Excel entry"
            
            # Parse SAP measurement date and time
            sap_datetime = self._parse_sap_datetime(sap_date, sap_time)
            if not sap_datetime:
                return False, "Invalid date/time format in SAP measurement history"
            
            # Convert SAP reading to float (handle Decimal objects from SAP)
            try:
                if hasattr(sap_reading, '__float__'):
                    # Handle Decimal objects
                    sap_reading_float = float(sap_reading)
                else:
                    # Handle string values
                    sap_reading_float = float(sap_reading)
            except (ValueError, TypeError):
                return False, f"Invalid SAP reading value: {sap_reading}"
            
            # Calculate time difference in hours
            time_diff = excel_datetime - sap_datetime
            time_diff_hours = time_diff.total_seconds() / 3600
            
            # Calculate hours difference
            hours_diff = curr_hrs - sap_reading_float
            
            logger.info(f"Validation check - Time diff: {time_diff_hours:.2f} hours, Hours diff: {hours_diff:.2f}")
            
            # Validation logic: time difference should be >= hours difference
            if time_diff_hours >= hours_diff:
                return True, "Validation passed"
            else:
                error_msg = (f"Incorrect Data: You’ve entered ({hours_diff:.2f} hours of usage in just ({time_diff_hours:.2f} hours. Please verify the reading, it looks too high!")
                return False, error_msg
                
        except Exception as e:
            return False, f"Error during hours validation: {str(e)}"
    
    def _parse_excel_datetime(self, date_str: str, time_str: str) -> Optional[datetime]:
        """
        Parse Excel date and time strings into datetime object.

        Args:
            date_str: Date string or datetime object (various formats like "2/2/25", "2025-02-02")
            time_str: Time string (formats like "8:40AM", "08:40", "8:40:00")

        Returns:
            datetime object or None if parsing fails
        """
        try:
            # Handle datetime objects from pandas/Excel
            if isinstance(date_str, datetime):
                parsed_date = date_str
                logger.info(f"Using datetime object directly: {parsed_date}")
            elif hasattr(date_str, 'to_pydatetime'):
                # Handle pandas Timestamp objects
                parsed_date = date_str.to_pydatetime()
                logger.info(f"Converted pandas Timestamp to datetime: {parsed_date}")
            else:
                # Handle different date string formats
                date_formats = [
                    '%m/%d/%y',    # 2/2/25
                    '%m/%d/%Y',    # 2/2/2025
                    '%Y-%m-%d',    # 2025-02-02
                    '%d/%m/%y',    # 2/2/25 (day/month/year)
                    '%d/%m/%Y',    # 2/2/2025 (day/month/year)
                ]

                parsed_date = None
                for date_format in date_formats:
                    try:
                        parsed_date = datetime.strptime(str(date_str).strip(), date_format)
                        break
                    except ValueError:
                        continue

                if not parsed_date:
                    logger.error(f"Could not parse date: {date_str} (type: {type(date_str)})")
                    return None
            
            # Handle different time formats
            time_formats = [
                '%I:%M%p',     # 8:40AM
                '%I:%M:%S%p',  # 8:40:00AM
                '%H:%M',       # 08:40
                '%H:%M:%S',    # 08:40:00
            ]
            
            time_str_clean = str(time_str).strip().upper()
            parsed_time = None
            
            for time_format in time_formats:
                try:
                    parsed_time = datetime.strptime(time_str_clean, time_format).time()
                    break
                except ValueError:
                    continue
            
            if not parsed_time:
                logger.error(f"Could not parse time: {time_str}")
                return None
            
            # Combine date and time
            combined_datetime = datetime.combine(parsed_date.date(), parsed_time)
            return combined_datetime
            
        except Exception as e:
            logger.error(f"Error parsing Excel datetime: {e}")
            return None
    
    def _parse_sap_datetime(self, date_str: str, time_str: str) -> Optional[datetime]:
        """
        Parse SAP date and time strings into datetime object.

        Args:
            date_str: SAP date string (YYYYMMDD format, e.g., '20250721')
            time_str: SAP time string (HHMMSS format, e.g., '171743')

        Returns:
            datetime object or None if parsing fails
        """
        try:
            # Parse SAP date (YYYYMMDD format)
            parsed_date = datetime.strptime(str(date_str).strip(), '%Y%m%d')

            # Parse SAP time (HHMMSS format) - need to add colons
            time_str_clean = str(time_str).strip()
            if len(time_str_clean) == 6:
                # Format: HHMMSS -> HH:MM:SS
                formatted_time = f"{time_str_clean[:2]}:{time_str_clean[2:4]}:{time_str_clean[4:6]}"
                parsed_time = datetime.strptime(formatted_time, '%H:%M:%S').time()
            else:
                logger.error(f"Invalid SAP time format: {time_str}")
                return None

            # Combine date and time
            combined_datetime = datetime.combine(parsed_date.date(), parsed_time)
            return combined_datetime

        except Exception as e:
            logger.error(f"Error parsing SAP datetime: {e}")
            return None


def validate_measurement_records(records: list, sap_client) -> Tuple[list, list]:
    """
    Validate multiple measurement records against SAP measurement history.
    
    Args:
        records: List of measurement record dictionaries
        sap_client: SAP RFC client instance
        
    Returns:
        Tuple of (valid_records, invalid_records)
        Each invalid record includes validation error message
    """
    validator = MeasurementValidator(sap_client)
    valid_records = []
    invalid_records = []
    
    for record in records:
        is_valid, error_message = validator.validate_measurement_record(record)
        
        if is_valid:
            valid_records.append(record)
        else:
            # Add validation error to record
            invalid_record = record.copy()
            invalid_record['validation_error'] = error_message
            invalid_records.append(invalid_record)
    
    return valid_records, invalid_records
