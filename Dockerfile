# Dockerfile for SAP RFC testing with PyRFC
# Force Intel/AMD64 architecture for SAP NW RFC SDK compatibility
FROM --platform=linux/amd64 ubuntu:22.04

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV RFC_NO_DECIMAL_CONV=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-dev \
    build-essential \
    wget \
    curl \
    unzip \
    libssl-dev \
    libffi-dev \
    libc6-dev \
    gcc \
    g++ \
    make \
    && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /app

# Create SAP NW RFC SDK directory structure
RUN mkdir -p /usr/local/sap/nwrfcsdk/lib /usr/local/sap/nwrfcsdk/include /usr/local/sap/nwrfcsdk/bin

# Copy SAP NW RFC SDK (you'll need to provide this)
# Note: You need to download the SAP NW RFC SDK from SAP Support Portal
# and place it in the build context as nwrfcsdk/
COPY nwrfcsdk/ /usr/local/sap/nwrfcsdk/

# Set SAP NW RFC SDK environment variables
ENV SAPNWRFC_HOME=/usr/local/sap/nwrfcsdk
ENV LD_LIBRARY_PATH=/usr/local/sap/nwrfcsdk/lib:$LD_LIBRARY_PATH
ENV PATH=/usr/local/sap/nwrfcsdk/bin:$PATH

# Install Python dependencies
RUN pip3 install --upgrade pip setuptools wheel

# Install Cython (required for PyRFC compilation)
RUN pip3 install cython

# Install PyRFC with specific version and build options
# Try different approaches based on what's available
RUN pip3 install pyrfc==3.3 || \
    pip3 install pyrfc==2.8.4 || \
    pip3 install pyrfc==2.7.3 || \
    pip3 install --no-cache-dir --force-reinstall pyrfc || \
    echo "PyRFC installation failed - will install manually"

# Alternative: Install from source if pip fails
RUN if ! python3 -c "import pyrfc" 2>/dev/null; then \
        echo "Installing PyRFC from source..." && \
        pip3 install wheel setuptools-scm && \
        pip3 install --no-cache-dir --no-binary=pyrfc pyrfc; \
    fi

# Install additional Python packages
RUN pip3 install \
    flask \
    pandas \
    openpyxl \
    python-dotenv

# Copy application files
COPY . /app/

# Create SAP environment file template
RUN echo '#!/bin/bash' > /app/sap_env_template.sh && \
    echo 'export SAP_ASHOST="your_sap_host"' >> /app/sap_env_template.sh && \
    echo 'export SAP_SYSNR="00"' >> /app/sap_env_template.sh && \
    echo 'export SAP_CLIENT="100"' >> /app/sap_env_template.sh && \
    echo 'export SAP_USER="your_username"' >> /app/sap_env_template.sh && \
    echo 'export SAP_PASSWD="your_password"' >> /app/sap_env_template.sh && \
    echo 'export SAP_LANG="EN"' >> /app/sap_env_template.sh && \
    chmod +x /app/sap_env_template.sh

# Set RFC environment variables to disable decimal conversion
ENV RFC_NO_DECIMAL_CONV=1
ENV RFC_TRACE=0

# Create entrypoint script
RUN echo '#!/bin/bash' > /app/entrypoint.sh && \
    echo 'echo "SAP RFC Docker Container"' >> /app/entrypoint.sh && \
    echo 'echo "======================="' >> /app/entrypoint.sh && \
    echo 'echo "Environment Variables:"' >> /app/entrypoint.sh && \
    echo 'echo "  RFC_NO_DECIMAL_CONV: $RFC_NO_DECIMAL_CONV"' >> /app/entrypoint.sh && \
    echo 'echo "  RFC_TRACE: $RFC_TRACE"' >> /app/entrypoint.sh && \
    echo 'echo "  SAPNWRFC_HOME: $SAPNWRFC_HOME"' >> /app/entrypoint.sh && \
    echo 'echo ""' >> /app/entrypoint.sh && \
    echo 'echo "SAP NW RFC SDK Info:"' >> /app/entrypoint.sh && \
    echo 'ls -la /usr/local/sap/nwrfcsdk/lib/ 2>/dev/null || echo "  SDK not found"' >> /app/entrypoint.sh && \
    echo 'echo ""' >> /app/entrypoint.sh && \
    echo 'echo "Python and PyRFC Info:"' >> /app/entrypoint.sh && \
    echo 'python3 --version' >> /app/entrypoint.sh && \
    echo 'python3 -c "import pyrfc; print(f\"PyRFC version: {pyrfc.__version__}\")" 2>/dev/null || echo "  PyRFC not available"' >> /app/entrypoint.sh && \
    echo 'echo ""' >> /app/entrypoint.sh && \
    echo 'if [ -f "/app/sap_env.sh" ]; then' >> /app/entrypoint.sh && \
    echo '    echo "Loading SAP environment from sap_env.sh..."' >> /app/entrypoint.sh && \
    echo '    source /app/sap_env.sh' >> /app/entrypoint.sh && \
    echo 'else' >> /app/entrypoint.sh && \
    echo '    echo "No sap_env.sh found. Copy sap_env_template.sh to sap_env.sh and configure."' >> /app/entrypoint.sh && \
    echo 'fi' >> /app/entrypoint.sh && \
    echo 'echo ""' >> /app/entrypoint.sh && \
    echo 'exec "$@"' >> /app/entrypoint.sh && \
    chmod +x /app/entrypoint.sh

# Expose port for Flask app
EXPOSE 5001

# Set entrypoint
# ENTRYPOINT ["/app/entrypoint.sh"]

# Default command
CMD ["bash"]
