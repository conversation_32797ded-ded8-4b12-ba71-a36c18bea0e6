"""
Excel parser module for measuring point data.
Handles reading and validating Excel files containing measuring point records.
"""

import pandas as pd
from typing import List, Dict, Any, Tuple
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MeasuringPointParser:
    """Parser for measuring point Excel files."""
    
    REQUIRED_COLUMNS = ['Meas-Point', 'Hours', 'Last Entry Date', 'Last Entry Time']
    EXPECTED_COLUMNS = [
        'Meas-Point', 'Manufacturer', 'Model', 'Unit ID', 'Hours',
        'Last Entry Date', 'Last Entry Time', 'INVESTIGATE', 'DEPARTMENT'
    ]
    
    def __init__(self):
        self.data = None
        self.errors = []
    
    def parse_excel_file(self, file_path: str) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Parse Excel file and return list of records and any errors.
        
        Args:
            file_path: Path to the Excel file
            
        Returns:
            Tuple of (records_list, errors_list)
        """
        try:
            # Read Excel file
            self.data = pd.read_excel(file_path, engine='openpyxl')
            logger.info(f"Successfully read Excel file with {len(self.data)} rows")
            
            # Validate structure
            validation_errors = self._validate_structure()
            if validation_errors:
                return [], validation_errors
            
            # Clean and process data
            records = self._process_data()
            
            return records, self.errors
            
        except Exception as e:
            error_msg = f"Error reading Excel file: {str(e)}"
            logger.error(error_msg)
            return [], [error_msg]
    
    def _validate_structure(self) -> List[str]:
        """Validate the Excel file structure."""
        errors = []
        
        if self.data is None or self.data.empty:
            errors.append("Excel file is empty or could not be read")
            return errors
        
        # Check for required columns
        missing_columns = []
        for col in self.REQUIRED_COLUMNS:
            if col not in self.data.columns:
                missing_columns.append(col)
        
        if missing_columns:
            errors.append(f"Missing required columns: {', '.join(missing_columns)}")
        
        # Log available columns for debugging
        logger.info(f"Available columns: {list(self.data.columns)}")
        
        return errors
    
    def _process_data(self) -> List[Dict[str, Any]]:
        """Process and clean the data."""
        records = []
        
        for index, row in self.data.iterrows():
            try:
                # Extract required fields
                meas_point = self._clean_meas_point(row['Meas-Point'])
                hours = self._clean_hours(row['Hours'])
                entry_date = self._safe_get_date_value(row, 'Last Entry Date')
                entry_time = self._safe_get_value(row, 'Last Entry Time')

                # Skip rows with invalid required data
                if meas_point is None or hours is None:
                    self.errors.append(f"Row {index + 2}: Invalid or missing required data (Meas-Point or Hours)")
                    continue

                # Validate date and time fields for phase 2 validation
                if not entry_date or not entry_time:
                    self.errors.append(f"Row {index + 2}: Missing required date/time data (Last Entry Date: '{entry_date}', Last Entry Time: '{entry_time}')")
                    continue
                
                # Create record with all available data
                record = {
                    'row_number': index + 2,  # Excel row number (accounting for header)
                    'meas_point': meas_point,
                    'hours': hours,
                    'manufacturer': self._safe_get_value(row, 'Manufacturer'),
                    'model': self._safe_get_value(row, 'Model'),
                    'unit_id': self._safe_get_value(row, 'Unit ID'),
                    'last_entry_date': self._safe_get_date_value(row, 'Last Entry Date'),
                    'last_entry_time': self._safe_get_value(row, 'Last Entry Time'),
                    'investigate': self._safe_get_value(row, 'INVESTIGATE'),
                    'department': self._safe_get_value(row, 'DEPARTMENT')
                }
                
                records.append(record)
                
            except Exception as e:
                error_msg = f"Row {index + 2}: Error processing row - {str(e)}"
                self.errors.append(error_msg)
                logger.error(error_msg)
        
        logger.info(f"Successfully processed {len(records)} records")
        return records
    
    def _clean_meas_point(self, value: Any) -> str:
        """Clean and validate measuring point value."""
        if pd.isna(value):
            return None
        
        # Convert to string and strip whitespace
        meas_point = str(value).strip()
        
        # Remove any decimal points if it's a number stored as float
        if '.' in meas_point and meas_point.replace('.', '').replace('0', '').isdigit():
            meas_point = str(int(float(meas_point)))
        
        # Validate it's not empty
        if not meas_point:
            return None
            
        return meas_point
    
    def _clean_hours(self, value: Any) -> float:
        """Clean and validate hours value."""
        if pd.isna(value):
            return None
        
        try:
            # Convert to float
            hours = float(value)
            
            # Validate it's a positive number
            if hours < 0:
                return None
                
            return hours
            
        except (ValueError, TypeError):
            return None
    
    def _safe_get_value(self, row: pd.Series, column: str) -> str:
        """Safely get value from row, returning empty string if not available."""
        if column not in row.index:
            return ""

        value = row[column]
        if pd.isna(value):
            return ""

        return str(value).strip()

    def _safe_get_date_value(self, row: pd.Series, column: str):
        """Safely get date value from row, preserving datetime objects."""
        if column not in row.index:
            return ""

        value = row[column]
        if pd.isna(value):
            return ""

        # If it's already a datetime object, return it as-is
        if isinstance(value, pd.Timestamp) or hasattr(value, 'date'):
            return value

        # Otherwise return as string
        return str(value).strip()


def parse_measuring_points_file(file_path: str) -> Tuple[List[Dict[str, Any]], List[str]]:
    """
    Convenience function to parse measuring points Excel file.
    
    Args:
        file_path: Path to the Excel file
        
    Returns:
        Tuple of (records_list, errors_list)
    """
    parser = MeasuringPointParser()
    return parser.parse_excel_file(file_path)
