# SAP RFC Docker Testing Environment

This Docker setup provides a controlled environment for testing PyRFC decimal conversion issues with the SAP NW RFC SDK.

## 🎯 Purpose

- Test PyRFC with different configurations
- Isolate decimal conversion issues
- Try different SAP NW RFC SDK versions
- Debug RFC calls in a clean environment

## 📋 Prerequisites

1. **Docker & Docker Compose** installed
2. **SAP NW RFC SDK** (download from SAP Support Portal)
3. **SAP connection details**

## 🚀 Quick Setup

### 1. Download SAP NW RFC SDK
```bash
# Download from SAP Support Portal and extract to:
mkdir nwrfcsdk
# Extract SDK files to nwrfcsdk/ directory
# Structure should be:
#   nwrfcsdk/lib/
#   nwrfcsdk/include/
#   nwrfcsdk/bin/
```

### 2. Configure SAP Connection
```bash
# Edit sap_env.sh with your SAP details
cp sap_env_template.sh sap_env.sh
nano sap_env.sh
```

### 3. Build and Run
```bash
# Run setup script
./docker-setup.sh

# Or manually:
docker-compose build
docker-compose run --rm sap-rfc-test python3 test_rfc.py
```

## 🐳 Available Services

### `sap-rfc-test` (Standard)
- `RFC_NO_DECIMAL_CONV=1` (disable decimal conversion)
- Standard testing environment
- Port: 5001

### `sap-rfc-test-trace` (With Tracing)
- `RFC_TRACE=1` (enable RFC tracing)
- `RFC_NO_DECIMAL_CONV=1`
- Captures detailed RFC communication
- Port: 5002

### `sap-rfc-test-alt` (Alternative Config)
- `RFC_NO_DECIMAL_CONV=0` (enable decimal conversion)
- `RFC_TRACE=1`
- Test with decimal conversion enabled
- Port: 5003

## 🔧 Usage Examples

### Run RFC Test
```bash
# Standard test
docker-compose run --rm sap-rfc-test python3 test_rfc.py

# With tracing
docker-compose run --rm sap-rfc-test-trace python3 test_rfc.py

# Alternative config
docker-compose run --rm sap-rfc-test-alt python3 test_rfc.py
```

### Interactive Shell
```bash
# Get shell access
docker-compose run --rm sap-rfc-test bash

# Inside container:
python3 test_rfc.py
python3 -c "import pyrfc; print(pyrfc.__version__)"
ls -la /usr/local/sap/nwrfcsdk/lib/
```

### Run Flask App
```bash
# Start Flask app in container
docker-compose run --rm -p 5001:5001 sap-rfc-test python3 app.py
```

## 🔍 Debugging

### Check Environment
```bash
docker-compose run --rm sap-rfc-test env | grep RFC
docker-compose run --rm sap-rfc-test python3 -c "import os; print(os.environ.get('RFC_NO_DECIMAL_CONV'))"
```

### View Trace Files
```bash
# Traces are saved to ./traces/ directory
docker-compose run --rm sap-rfc-test-trace python3 test_rfc.py
cat traces/dev_rfc.log
```

### Test Different Configurations
```bash
# Test with decimal conversion disabled
docker-compose run --rm -e RFC_NO_DECIMAL_CONV=1 sap-rfc-test python3 test_rfc.py

# Test with decimal conversion enabled
docker-compose run --rm -e RFC_NO_DECIMAL_CONV=0 sap-rfc-test python3 test_rfc.py

# Test with custom environment
docker-compose run --rm -e RFC_TRACE=1 -e RFC_NO_DECIMAL_CONV=1 sap-rfc-test python3 test_rfc.py
```

## 📁 Directory Structure

```
.
├── Dockerfile              # Docker image definition
├── docker-compose.yml      # Multi-service configuration
├── docker-setup.sh         # Setup script
├── nwrfcsdk/               # SAP NW RFC SDK (you provide)
│   ├── lib/
│   ├── include/
│   └── bin/
├── sap_env.sh              # SAP connection config
├── test_rfc.py             # Standalone RFC test
├── logs/                   # Application logs
└── traces/                 # RFC trace files
```

## 🎯 Testing Strategy

1. **Test with `RFC_NO_DECIMAL_CONV=1`**
   - Should prevent decimal conversion errors
   - Check if RFC calls succeed

2. **Test with RFC tracing**
   - Capture exact SAP response data
   - See what causes decimal conversion issues

3. **Compare different configurations**
   - Different PyRFC versions
   - Different environment variables
   - Different parameter formats

## 🐛 Troubleshooting

### PyRFC Installation Fails
```bash
# Check SAP NW RFC SDK
docker-compose run --rm sap-rfc-test ls -la /usr/local/sap/nwrfcsdk/lib/

# Check environment variables
docker-compose run --rm sap-rfc-test env | grep SAP
```

### Connection Issues
```bash
# Test SAP connectivity
docker-compose run --rm sap-rfc-test python3 -c "
from pyrfc import Connection
conn = Connection(ashost='your_host', sysnr='00', client='100', user='user', passwd='pass')
print('Connected successfully')
conn.close()
"
```

### Decimal Conversion Issues
```bash
# Test with different settings
docker-compose run --rm -e RFC_NO_DECIMAL_CONV=1 sap-rfc-test python3 test_rfc.py
docker-compose run --rm -e RFC_NO_DECIMAL_CONV=0 sap-rfc-test python3 test_rfc.py
```

## 🎉 Expected Results

With `RFC_NO_DECIMAL_CONV=1`, you should see:
- ✅ RFC calls succeed without decimal conversion errors
- ✅ Real measurement document numbers returned
- ✅ Clean response processing

This Docker environment isolates the PyRFC issue and tests the `RFC_NO_DECIMAL_CONV` solution!
