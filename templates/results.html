<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Results - Measuring Point Upload</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="display-5">
                        <i class="fas fa-chart-bar text-primary"></i>
                        Upload Results
                    </h1>
                    <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-upload"></i>
                        Upload Another File
                    </a>
                </div>
            </div>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'warning' if category == 'warning' else 'success' }} alert-dismissible fade show" role="alert">
                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'exclamation-circle' if category == 'warning' else 'check-circle' }}"></i>
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-file-excel fa-2x text-info mb-2"></i>
                        <h5 class="card-title">File Processed</h5>
                        <p class="card-text">{{ filename }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <i class="fas fa-list fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">Total Records</h5>
                        <p class="card-text display-6">{{ results|length }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-success">
                    <div class="card-body">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <h5 class="card-title text-success">Successful</h5>
                        <p class="card-text display-6 text-success">{{ success_count }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-danger">
                    <div class="card-body">
                        <i class="fas fa-exclamation-circle fa-2x text-danger mb-2"></i>
                        <h5 class="card-title text-danger">Failed</h5>
                        <p class="card-text display-6 text-danger">{{ error_count }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Results Table -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table"></i>
                    Detailed Results
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-success" onclick="filterResults('success')">
                        <i class="fas fa-filter"></i> Show Success Only
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="filterResults('error')">
                        <i class="fas fa-filter"></i> Show Errors Only
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="filterResults('all')">
                        <i class="fas fa-list"></i> Show All
                    </button>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="resultsTable">
                        <thead class="table-light">
                            <tr>
                                <th>Row</th>
                                <th>Status</th>
                                <th>Meas-Point</th>
                                <th>Hours</th>
                                <th>Last Entry Date</th>
                                <th>Last Entry Time</th>
                                <th>Manufacturer</th>
                                <th>Model</th>
                                <th>Unit ID</th>
                                <th>Message</th>
                                <th>Document</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for result in results %}
                            <tr class="result-row {{ 'table-success' if result.success else 'table-danger' }}" 
                                data-status="{{ 'success' if result.success else 'error' }}">
                                <td>{{ result.record.row_number }}</td>
                                <td>
                                    {% if result.success %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-check"></i> Success
                                        </span>
                                    {% else %}
                                        <span class="badge bg-danger">
                                            <i class="fas fa-times"></i> Failed
                                        </span>
                                    {% endif %}
                                </td>
                                <td><strong>{{ result.record.meas_point }}</strong></td>
                                <td>{{ result.record.hours }}</td>
                                <td>{{ result.record.last_entry_date or '-' }}</td>
                                <td>{{ result.record.last_entry_time or '-' }}</td>
                                <td>{{ result.record.manufacturer or '-' }}</td>
                                <td>{{ result.record.model or '-' }}</td>
                                <td>{{ result.record.unit_id or '-' }}</td>
                                <td>
                                    {% if result.success %}
                                        <span class="text-success">{{ result.message }}</span>
                                    {% else %}
                                        <span class="text-danger">{{ result.message }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if result.measurement_document %}
                                        <code>{{ result.measurement_document }}</code>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i>
                            Processing Summary
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><strong>File:</strong> {{ filename }}</li>
                            <li><strong>Total Records:</strong> {{ results|length }}</li>
                            <li><strong>Success Rate:</strong> 
                                {% set success_rate = (success_count / results|length * 100) if results|length > 0 else 0 %}
                                {{ "%.1f"|format(success_rate) }}%
                            </li>
                            <li><strong>Processing Time:</strong> <span id="processingTime">-</span></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-question-circle"></i>
                            Next Steps
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            {% if error_count > 0 %}
                                <li><i class="fas fa-exclamation-triangle text-warning"></i> Review failed records above</li>
                                <li><i class="fas fa-edit text-info"></i> Correct data in your Excel file</li>
                                <li><i class="fas fa-redo text-primary"></i> Re-upload corrected records</li>
                            {% else %}
                                <li><i class="fas fa-check-circle text-success"></i> All records processed successfully</li>
                                <li><i class="fas fa-database text-info"></i> Data is now available in SAP ECC</li>
                            {% endif %}
                            <li><i class="fas fa-upload text-primary"></i> <a href="{{ url_for('index') }}">Upload another file</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Filter results function
        function filterResults(filter) {
            const rows = document.querySelectorAll('.result-row');
            
            rows.forEach(row => {
                const status = row.getAttribute('data-status');
                
                if (filter === 'all') {
                    row.style.display = '';
                } else if (filter === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // Set processing time (placeholder - in real app this would come from backend)
        document.getElementById('processingTime').textContent = 'Just now';
    </script>
</body>
</html>
