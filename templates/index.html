<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Measuring Point Upload - SAP ECC</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <!-- Header -->
                <div class="text-center mb-4">
                    <h1 class="display-4">
                        <i class="fas fa-upload text-primary"></i>
                        Measuring Point Upload
                    </h1>
                    <p class="lead text-muted">Upload Excel files with measuring point data to SAP ECC</p>
                </div>

                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else 'warning' if category == 'warning' else 'success' }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'exclamation-circle' if category == 'warning' else 'check-circle' }}"></i>
                                <pre class="mb-0">{{ message }}</pre>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Upload Form -->
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-excel"></i>
                            Upload Excel File
                        </h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ url_for('upload_file') }}" method="post" enctype="multipart/form-data" id="uploadForm">
                            <div class="mb-3">
                                <label for="file" class="form-label">Select Excel File (.xlsx or .xls)</label>
                                <input type="file" class="form-control" id="file" name="file" accept=".xlsx,.xls" required>
                                <div class="form-text">Maximum file size: 16MB</div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg" id="uploadBtn">
                                    <i class="fas fa-upload"></i>
                                    Upload and Process
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- File Format Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle"></i>
                            Expected File Format
                        </h5>
                    </div>
                    <div class="card-body">
                        <p>Your Excel file should contain the following columns:</p>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>Meas-Point <span class="text-danger">*</span></th>
                                        <th>Manufacturer</th>
                                        <th>Model</th>
                                        <th>Unit ID</th>
                                        <th>Hours <span class="text-danger">*</span></th>
                                        <th>Last Entry Date</th>
                                        <th>Last Entry Time</th>
                                        <th>INVESTIGATE</th>
                                        <th>DEPARTMENT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>1000152</td>
                                        <td>KOMATSU</td>
                                        <td>830E-1AC</td>
                                        <td>201</td>
                                        <td>65024</td>
                                        <td>2/2/25</td>
                                        <td>8:40AM</td>
                                        <td>155</td>
                                        <td>Mine Ops</td>
                                    </tr>
                                    <tr>
                                        <td>1000153</td>
                                        <td>KOMATSU</td>
                                        <td>830E-1AC</td>
                                        <td>202</td>
                                        <td>66038</td>
                                        <td>2/2/25</td>
                                        <td>8:40AM</td>
                                        <td>155</td>
                                        <td>Mine Ops</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <p class="text-muted">
                            <small>
                                <span class="text-danger">*</span> Required fields. 
                                Only <strong>Meas-Point</strong> and <strong>Hours</strong> are used for SAP upload. 
                                Other columns are retained for logging and error display.
                            </small>
                        </p>
                    </div>
                </div>

                <!-- Processing Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-cogs"></i>
                            How It Works
                        </h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>Upload your Excel file using the form above</li>
                            <li>The system will parse and validate each row</li>
                            <li>Each valid record will be uploaded to SAP ECC</li>
                            <li>Results will be displayed showing success/failure status for each record</li>
                            <li>Any errors will be clearly indicated with detailed error messages</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    <div class="modal fade" id="loadingModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5>Processing your file...</h5>
                    <p class="text-muted">Please wait while we upload your measuring points to SAP ECC.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Show loading modal on form submission
        document.getElementById('uploadForm').addEventListener('submit', function() {
            const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
            loadingModal.show();
            
            // Disable the upload button
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        });

        // File validation
        document.getElementById('file').addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const fileSize = file.size / 1024 / 1024; // Convert to MB
                if (fileSize > 16) {
                    alert('File size exceeds 16MB limit. Please choose a smaller file.');
                    this.value = '';
                }
            }
        });
    </script>
</body>
</html>
