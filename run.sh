#!/bin/bash

# Quick startup script for Measuring Point Upload Application
# Simple version that loads SAP environment and starts the app

source ~/.zshrc

# Load SAP environment if available
if [ -f "sap_env.sh" ]; then
    echo "🔧 Loading SAP environment variables..."
    source ./sap_env.sh
else
    echo "⚠️  No sap_env.sh found - using mock SAP functionality"
fi

# Find available port
PORT=5000
while lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null 2>&1; do
    echo "Port $PORT is busy, trying $((PORT + 1))"
    PORT=$((PORT + 1))
done

echo "🚀 Starting application on port $PORT..."
echo "📱 Access at: http://localhost:$PORT"

# Export port and start app
export PORT=$PORT
python3 app.py
