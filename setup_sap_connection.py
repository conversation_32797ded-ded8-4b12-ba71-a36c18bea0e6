#!/usr/bin/env python3
"""
Interactive script to set up SAP connection parameters.
"""

import os
import getpass
import sys

def get_sap_parameters():
    """Interactively collect SAP connection parameters."""
    print("=" * 60)
    print("SAP CONNECTION SETUP")
    print("=" * 60)
    print("Please provide your SAP connection details:")
    print()
    
    params = {}
    
    # SAP Application Server Host
    params['ashost'] = input("SAP Application Server Host (e.g., sap-server.company.com): ").strip()
    if not params['ashost']:
        print("❌ SAP Host is required!")
        return None
    
    # System Number
    params['sysnr'] = input("SAP System Number [00]: ").strip() or "00"
    
    # Client
    params['client'] = input("SAP Client [100]: ").strip() or "100"
    
    # Username
    params['user'] = input("SAP Username: ").strip()
    if not params['user']:
        print("❌ SAP Username is required!")
        return None
    
    # Password
    params['passwd'] = getpass.getpass("SAP Password: ")
    if not params['passwd']:
        print("❌ SAP Password is required!")
        return None
    
    # Language
    params['lang'] = input("SAP Language [EN]: ").strip() or "EN"
    
    # Optional: SAP Router
    router = input("SAP Router (optional, press Enter to skip): ").strip()
    if router:
        params['saprouter'] = router
    
    return params

def create_env_file(params):
    """Create a .env file with SAP parameters."""
    env_content = f"""# SAP Connection Parameters
# Generated by setup_sap_connection.py

SAP_ASHOST={params['ashost']}
SAP_SYSNR={params['sysnr']}
SAP_CLIENT={params['client']}
SAP_USER={params['user']}
SAP_PASSWD={params['passwd']}
SAP_LANG={params['lang']}
"""
    
    if 'saprouter' in params:
        env_content += f"SAP_SAPROUTER={params['saprouter']}\n"
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print(f"✅ Created .env file with SAP connection parameters")

def create_export_script(params):
    """Create a shell script to export environment variables."""
    script_content = f"""#!/bin/bash
# SAP Connection Environment Variables
# Generated by setup_sap_connection.py
# Usage: source sap_env.sh

export SAP_ASHOST="{params['ashost']}"
export SAP_SYSNR="{params['sysnr']}"
export SAP_CLIENT="{params['client']}"
export SAP_USER="{params['user']}"
export SAP_PASSWD="{params['passwd']}"
export SAP_LANG="{params['lang']}"
"""
    
    if 'saprouter' in params:
        script_content += f'export SAP_SAPROUTER="{params["saprouter"]}"\n'
    
    script_content += '\necho "✅ SAP environment variables loaded"\n'
    
    with open('sap_env.sh', 'w') as f:
        f.write(script_content)
    
    # Make script executable
    os.chmod('sap_env.sh', 0o755)
    
    print(f"✅ Created sap_env.sh script")

def test_connection(params):
    """Test the SAP connection with provided parameters."""
    print("\nTesting SAP connection...")
    
    try:
        from pyrfc import Connection, LogonError, CommunicationError
        
        conn = Connection(**params)
        print("✅ SAP connection successful!")
        
        # Test RFC call
        try:
            result = conn.call('RFC_SYSTEM_INFO')
            system_info = result.get('RFCSI_EXPORT', {})
            print(f"✅ RFC test successful!")
            print(f"   System: {system_info.get('RFCHOST', 'Unknown')}")
            print(f"   Release: {system_info.get('RFCREL', 'Unknown')}")
        except Exception as e:
            print(f"⚠️  RFC test failed: {e}")
        
        conn.close()
        return True
        
    except LogonError as e:
        print(f"❌ SAP Logon Error: {e}")
        return False
    except CommunicationError as e:
        print(f"❌ SAP Communication Error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main setup function."""
    print("This script will help you set up SAP connection parameters.")
    print("Make sure you have:")
    print("1. SAP NetWeaver RFC SDK installed")
    print("2. Network access to your SAP system")
    print("3. Valid SAP user credentials")
    print()
    
    # Get parameters
    params = get_sap_parameters()
    if not params:
        print("❌ Setup cancelled due to missing required parameters.")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("CONFIGURATION SUMMARY")
    print("=" * 60)
    print(f"SAP Host: {params['ashost']}")
    print(f"System Number: {params['sysnr']}")
    print(f"Client: {params['client']}")
    print(f"Username: {params['user']}")
    print(f"Language: {params['lang']}")
    if 'saprouter' in params:
        print(f"SAP Router: {params['saprouter']}")
    
    # Confirm
    confirm = input("\nProceed with this configuration? (y/N): ").strip().lower()
    if confirm != 'y':
        print("Setup cancelled.")
        sys.exit(0)
    
    # Test connection
    if test_connection(params):
        print("\n🎉 SAP connection test successful!")
        
        # Create configuration files
        create_env_file(params)
        create_export_script(params)
        
        print("\n" + "=" * 60)
        print("SETUP COMPLETE")
        print("=" * 60)
        print("Configuration files created:")
        print("  - .env (for application)")
        print("  - sap_env.sh (for shell environment)")
        print()
        print("To use the configuration:")
        print("1. Load environment: source sap_env.sh")
        print("2. Start application: python3 app.py")
        print("3. The app will use real SAP connection")
        
    else:
        print("\n❌ SAP connection test failed.")
        print("Please check your parameters and try again.")
        
        # Still create files for manual correction
        create_env_file(params)
        create_export_script(params)
        print("\nConfiguration files created for manual correction.")

if __name__ == '__main__':
    main()
