# Measuring Point Upload Web App for SAP ECC

## Overview

This is a simple Python web application that allows users to upload an Excel (.xlsx) file containing manually recorded measuring points for large land-moving vehicles and equipment. Each row in the Excel file is uploaded to SAP ECC using the RFC `MEASUREM_DOCUM_RFC_SINGLE_001`. Errors during upload are logged and presented in a readable tabular view in the web UI.

---

## Features

- Upload Excel file via web UI
- Parse and validate rows using `pandas`
- Call SAP ECC RFC `MEASUREM_DOCUM_RFC_SINGLE_001` for each record using `PyRFC`
- Capture errors for individual records
- Display a summary table of success/failure per record to the user

---

## Tech Stack

- **Frontend**: HTML (via <PERSON><PERSON><PERSON>'s <PERSON><PERSON> templates), Bootstrap (optional)
- **Backend**: Python, Flask
- **Excel Processing**: `pandas`, `openpyxl`
- **SAP RFC Access**: `PyRFC` library
- **Runtime**: Python 3.8+

---

## File Format

Excel input is expected in the following format:

| Meas-Point | Manufacturer | Model   | Unit ID | Hours  | Last Entry Date | Last Entry Time | INVESTIGATE | DEPARTMENT |
|------------|--------------|---------|---------|--------|------------------|------------------|-------------|------------|
| 1000152    | KOMATSU      | 830E-1AC| 201     | 65024  | 2/2/25           | 8:40AM           | 155         | Mine Ops   |
| 1000153    | KOMATSU      | 830E-1AC| 202     | 66038  | 2/2/25           | 8:40AM           | 155         | Mine Ops   |

Only `Meas-Point` and `Hours` are used in the RFC call. Others are retained for logging and error display.

**Important:**
- The `Hours` value must be converted to string before passing to the SAP RFC function, as it expects a STRING type parameter, not FLOAT.
- The `Measuring Point` must be formatted as a 12-character string with leading zeros (e.g., "61" becomes "000000000061") using the `alphaIN` function.

---

## SAP RFC Call Structure

```abap
CALL FUNCTION 'MEASUREM_DOCUM_RFC_SINGLE_001'
  EXPORTING
    measurement_point    = lv_mpoint
    recorded_value       = reading_value  " Must be STRING type
  IMPORTING
    measurement_document = measurement_document
  EXCEPTIONS
    no_authority         = 1
    point_not_found      = 2
    index_not_unique     = 3
    type_not_found       = 4
    point_locked         = 5
    point_inactive       = 6
    timestamp_in_future  = 7
    timestamp_duprec     = 8
    unit_unfit           = 9
    value_not_fltp       = 10
    value_overflow       = 11
    value_unfit          = 12
    value_missing        = 13
    code_not_found       = 14
    notif_type_not_found = 15
    notif_prio_not_found = 16
    notif_gener_problem  = 17
    update_failed        = 18
    invalid_time         = 19
    invalid_date         = 20
    OTHERS               = 21.
```

## Folder Structure
```
measuring-point-uploader/
│
├── app.py                # Flask app
├── templates/
│   └── index.html        # File upload UI & results
├── static/
│   └── style.css         # Optional styling
├── requirements.txt      # Python dependencies
├── sap_utils.py          # SAP RFC wrapper function
├── parser.py             # Excel parser and validator
└── README.md             # This file
```

## requirements.txt 
```
flask
pandas
openpyxl
pyrfc
```

## UI Flow
- User lands on the upload page.
- Uploads the .xlsx file using the upload form.
- Backend parses the file using pandas.
- For each row:
- Extract Meas-Point and Hours
- Call SAP RFC
- Record success or detailed failure
- Final result is shown as an HTML table:
- Green rows for success
- Red rows for errors with message