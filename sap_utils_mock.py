"""
Mock SAP RFC utility module for testing purposes.
This simulates the SAP RFC functionality without requiring actual SAP connection.
"""

from typing import Dict, Any, Tuple, Optional
import logging
import random
import time

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def alphaIN(input_str: str, length: int) -> str:
    """
    SAP Alpha conversion function - pads input string with leading zeros.

    This function mimics SAP's ALPHA conversion for internal format.
    It pads the input string with leading zeros to reach the specified length.

    Args:
        input_str: Input string to be padded
        length: Target length for the output string

    Returns:
        String padded with leading zeros to the specified length

    Examples:
        alphaIN("61", 12) -> "000000000061"
        alphaIN("1000152", 12) -> "000001000152"
        alphaIN("123456789012", 12) -> "123456789012"
    """
    if not isinstance(input_str, str):
        input_str = str(input_str)

    # Remove any existing leading/trailing whitespace
    input_str = input_str.strip()

    # Pad with leading zeros to reach the target length
    return input_str.zfill(length)


def alphaOUT(input_str: str) -> str:
    """
    SAP Alpha conversion function - removes leading zeros from string.

    This function mimics SAP's ALPHA conversion for external format.
    It removes leading zeros from the input string.

    Args:
        input_str: Input string with potential leading zeros

    Returns:
        String with leading zeros removed

    Examples:
        alphaOUT("000000000061") -> "61"
        alphaOUT("000001000152") -> "1000152"
        alphaOUT("123456789012") -> "123456789012"
        alphaOUT("000000000000") -> "0"
    """
    if not isinstance(input_str, str):
        input_str = str(input_str)

    # Remove any existing leading/trailing whitespace
    input_str = input_str.strip()

    # Remove leading zeros, but keep at least one digit
    result = input_str.lstrip('0')

    # If all characters were zeros, return "0"
    if not result:
        result = "0"

    return result


class MockSAPRFCClient:
    """Mock SAP RFC client for testing purposes."""
    
    # RFC Exception codes and their descriptions
    RFC_EXCEPTIONS = {
        1: "no_authority - No authorization for this operation",
        2: "point_not_found - Measuring point not found",
        3: "index_not_unique - Index is not unique",
        4: "type_not_found - Type not found",
        5: "point_locked - Measuring point is locked",
        6: "point_inactive - Measuring point is inactive",
        7: "timestamp_in_future - Timestamp is in the future",
        8: "timestamp_duprec - Duplicate timestamp record",
        9: "unit_unfit - Unit is not suitable",
        10: "value_not_fltp - Value is not a floating point number",
        11: "value_overflow - Value overflow",
        12: "value_unfit - Value is not suitable",
        13: "value_missing - Value is missing",
        14: "code_not_found - Code not found",
        15: "notif_type_not_found - Notification type not found",
        16: "notif_prio_not_found - Notification priority not found",
        17: "notif_gener_problem - Notification generation problem",
        18: "update_failed - Update failed",
        19: "invalid_time - Invalid time",
        20: "invalid_date - Invalid date",
        21: "OTHERS - Other error"
    }
    
    def __init__(self, connection_params: Dict[str, str]):
        """Initialize mock SAP RFC client."""
        self.connection_params = connection_params
        self.connection = None
        self.connected = False
    
    def connect(self) -> Tuple[bool, str]:
        """Mock connection to SAP system."""
        # Simulate connection delay
        time.sleep(0.5)
        
        # Mock connection success
        self.connected = True
        logger.info("Mock: Successfully connected to SAP system")
        return True, "Mock connection successful"
    
    def disconnect(self):
        """Mock disconnect from SAP system."""
        if self.connected:
            self.connected = False
            logger.info("Mock: Disconnected from SAP system")
    
    def upload_measuring_point(self, meas_point: str, hours: float) -> Tuple[bool, str, Optional[str]]:
        """Mock upload of a single measuring point record to SAP."""
        if not self.connected:
            return False, "Not connected to SAP system", None

        # Convert measuring point to SAP internal format (12 characters with leading zeros)
        meas_point_formatted = alphaIN(meas_point, 12)

        # Convert hours to string in SAP-compatible format (same as real SAP implementation)
        if isinstance(hours, (int, float)):
            if float(hours).is_integer():
                hours_str = str(int(float(hours)))  # Convert 65024.0 -> "65024"
            else:
                # For decimals, format without unnecessary trailing zeros but avoid scientific notation
                hours_str = f"{float(hours):.10f}".rstrip('0').rstrip('.')
        else:
            hours_str = str(hours)

        # Simulate processing delay
        time.sleep(0.1)
        
        # Mock some failures for testing (10% failure rate)
        if random.random() < 0.1:
            error_code = random.choice([2, 5, 6, 18])  # Common error codes
            error_description = self.RFC_EXCEPTIONS.get(error_code, "Unknown error")
            error_msg = f"Mock SAP Error: {error_description}"
            logger.error(f"Mock ABAP Application Error for {meas_point}: {error_msg}")
            return False, error_msg, None
        
        # Mock successful upload - generate document with leading zeros, then trim
        measurement_document_raw = f"000000DOC{random.randint(1000000, 9999999)}"
        measurement_document = alphaOUT(measurement_document_raw)
        success_msg = f"Mock: Successfully uploaded measuring point {meas_point} ({meas_point_formatted}) with {hours_str} hours"
        logger.info(success_msg)
        
        return True, success_msg, measurement_document
    
    def test_connection(self) -> Tuple[bool, str]:
        """Mock test the SAP connection."""
        if not self.connected:
            return False, "Not connected to SAP system"

        success_msg = "Mock: Connection test successful. System: MOCK_SAP_SYSTEM"
        logger.info(success_msg)
        return True, success_msg

    def get_measurement_history(self, meas_point: str) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Mock get measurement history for a measuring point using ALM_MEREP_041_GETDETAIL RFC.

        Args:
            meas_point: Measuring point identifier

        Returns:
            Tuple of (success, message, measurement_history_data)
            measurement_history_data contains: {'MDOCM': str, 'IDATE': str, 'READG': str, 'ITIME': str}
        """
        if not self.connected:
            return False, "Not connected to SAP system", None

        # Convert measuring point to SAP internal format (12 characters with leading zeros)
        meas_point_formatted = alphaIN(meas_point, 12)

        # Simulate processing delay
        time.sleep(0.1)

        # Mock some failures for testing (5% failure rate)
        if random.random() < 0.05:
            error_msg = f"Mock: No measurement history found for measuring point {meas_point}"
            logger.error(error_msg)
            return False, error_msg, None

        # Generate mock measurement history data
        from datetime import datetime, timedelta

        # Create a mock date that's 1-30 days ago
        days_ago = random.randint(1, 30)
        mock_date = datetime.now() - timedelta(days=days_ago)

        # Generate mock reading that's slightly less than typical hours
        base_hours = random.randint(50000, 70000)
        mock_reading = base_hours - random.randint(10, 100)

        measurement_data = {
            'MDOCM': f"DOC{random.randint(1000000, 9999999)}",
            'IDATE': mock_date.strftime('%Y-%m-%d'),
            'READG': str(mock_reading),
            'ITIME': mock_date.strftime('%H:%M:%S')
        }

        success_msg = f"Mock: Successfully retrieved measurement history for {meas_point}"
        logger.info(success_msg)
        return True, success_msg, measurement_data


def get_sap_connection_params() -> Dict[str, str]:
    """Get mock SAP connection parameters."""
    return {
        'ashost': 'mock-sap-server.com',
        'sysnr': '00',
        'client': '100',
        'user': 'mock-user',
        'passwd': 'mock-password',
        'lang': 'EN'
    }


def upload_measuring_points_batch(records: list) -> list:
    """
    Mock upload of multiple measuring point records to SAP.
    
    Args:
        records: List of dictionaries with 'meas_point' and 'hours' keys
        
    Returns:
        List of result dictionaries with upload status for each record
    """
    connection_params = get_sap_connection_params()
    sap_client = MockSAPRFCClient(connection_params)
    
    # Connect to SAP
    connected, connect_msg = sap_client.connect()
    if not connected:
        # Return error for all records if connection fails
        return [
            {
                'record': record,
                'success': False,
                'message': f"Connection failed: {connect_msg}",
                'measurement_document': None
            }
            for record in records
        ]
    
    results = []
    
    try:
        # Process each record
        for record in records:
            meas_point = record.get('meas_point')
            hours = record.get('hours')
            
            if not meas_point or hours is None:
                results.append({
                    'record': record,
                    'success': False,
                    'message': "Missing required data (meas_point or hours)",
                    'measurement_document': None
                })
                continue
            
            # Upload the record
            success, message, measurement_document = sap_client.upload_measuring_point(
                meas_point, hours
            )
            
            results.append({
                'record': record,
                'success': success,
                'message': message,
                'measurement_document': measurement_document
            })
    
    finally:
        # Always disconnect
        sap_client.disconnect()
    
    return results
