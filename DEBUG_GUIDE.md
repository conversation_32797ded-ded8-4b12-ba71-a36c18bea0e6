# Debug Guide for Measuring Point Upload Application

This guide explains how to debug the web application using various methods.

## 🐛 **Quick Debug Start**

### **Option 1: Command Line Debug Script**
```bash
./debug.sh
```

### **Option 2: VS Code Integrated Debugging**
1. Open VS Code in this directory: `code .`
2. Go to **Run and Debug** (Ctrl+Shift+D or Cmd+Shift+D)
3. Select one of these configurations:
   - **"Python Debugger: Flask"** - Direct app.py execution
   - **"Flask Debug with SAP Environment"** - Loads .env file with SAP settings
   - **"Flask Module Debug"** - Uses Flask module approach
4. Press **F5** to start debugging
5. Web server will start at http://localhost:5001

### **Option 3: Manual Debug Mode**
```bash
source ./sap_env.sh
export FLASK_DEBUG=1
python3 -m flask run --debug --port=5001
```

## 🔧 **Debug Script Features**

The `debug.sh` script provides:

- ✅ **Auto-reload** on code changes
- ✅ **Detailed error pages** with stack traces
- ✅ **Enhanced logging** with debug information
- ✅ **SAP environment loading**
- ✅ **Dependency checking**
- ✅ **Port conflict resolution**
- ✅ **Colored output** for better readability

### **Debug Script Options**
```bash
./debug.sh              # Start debug server
./debug.sh -p 8080       # Start on specific port
./debug.sh -v            # Verbose mode
./debug.sh --help        # Show help
```

## 🎯 **VS Code Debug Configurations**

### **Configuration 1: Python Debugger: Flask**
- Standard Flask debugging
- Loads SAP environment from `sap_env.sh`
- Runs on port 5001
- Integrated terminal

### **Configuration 2: Python Debugger: Flask (with SAP)**
- Direct Python execution
- Pre-launch task to load SAP environment
- Better for debugging SAP-specific issues

## 🔍 **Debugging Different Components**

### **1. Excel Parser Debugging**
```bash
# Test parser separately
python3 test_parser.py

# Debug parser with breakpoints
python3 -c "
import pdb; pdb.set_trace()
from parser import parse_measuring_points_file
records, errors = parse_measuring_points_file('sample_measuring_points.xlsx')
"
```

### **2. SAP Connection Debugging**
```bash
# Test SAP connection
python3 test_pyrfc.py

# Debug SAP utils
python3 -c "
import pdb; pdb.set_trace()
from sap_utils import SAPRFCClient, get_sap_connection_params
client = SAPRFCClient(get_sap_connection_params())
"
```

### **3. Web Interface Debugging**
- Use browser developer tools (F12)
- Check Network tab for API calls
- Monitor Console for JavaScript errors
- Use Flask debug toolbar (if installed)

## 📊 **Debug Logging Levels**

The application uses different logging levels:

- **DEBUG**: Detailed information for debugging
- **INFO**: General information about application flow
- **WARNING**: Warning messages for potential issues
- **ERROR**: Error messages for failures

### **Enable Debug Logging**
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 🚨 **Common Debug Scenarios**

### **1. SAP Connection Issues**
```bash
# Check SAP environment
echo $SAP_ASHOST
echo $SAP_USER

# Test connection
python3 test_pyrfc.py

# Debug with verbose logging
FLASK_DEBUG=1 python3 app.py
```

### **2. File Upload Issues**
- Check file permissions
- Verify file format (Excel .xlsx/.xls)
- Check file size limits (16MB max)
- Monitor temporary file creation

### **3. Measuring Point Format Issues**
```bash
# Test alphaIN/alphaOUT functions
python3 test_alpha_functions.py

# Debug specific measuring point
python3 -c "
from sap_utils import alphaIN, alphaOUT
print(f'Input: 61 -> alphaIN: {alphaIN(\"61\", 12)} -> alphaOUT: {alphaOUT(alphaIN(\"61\", 12))}')
"
```

## 🛠️ **VS Code Debug Tasks**

Available tasks in VS Code (Ctrl+Shift+P -> "Tasks: Run Task"):

1. **Load SAP Environment** - Sources SAP environment variables
2. **Start Debug Server** - Runs the debug.sh script
3. **Test SAP Connection** - Tests PyRFC connectivity
4. **Run All Tests** - Executes complete test suite

## 🔧 **Breakpoint Strategies**

### **Strategic Breakpoint Locations**

1. **File Upload Handler** (`app.py:upload_file`)
   ```python
   # Set breakpoint here to debug file processing
   records, parse_errors = parse_measuring_points_file(file_path)
   ```

2. **SAP RFC Call** (`sap_utils.py:upload_measuring_point`)
   ```python
   # Set breakpoint here to debug SAP communication
   result = self.connection.call(
       'MEASUREM_DOCUM_RFC_SINGLE_001',
       MEASUREMENT_POINT=meas_point_formatted,
       RECORDED_VALUE=hours_str
   )
   ```

3. **Excel Parsing** (`parser.py:_process_data`)
   ```python
   # Set breakpoint here to debug data extraction
   meas_point = self._clean_meas_point(row['Meas-Point'])
   hours = self._clean_hours(row['Hours'])
   ```

## 📱 **Browser Debug Tools**

### **Network Tab Monitoring**
- Monitor `/upload` POST requests
- Check request payload and response
- Verify file upload progress

### **Console Debugging**
- Check for JavaScript errors
- Monitor AJAX calls
- Debug form submissions

## 🎯 **Performance Debugging**

### **Profile SAP Calls**
```python
import time
start_time = time.time()
# SAP RFC call here
end_time = time.time()
print(f"SAP call took: {end_time - start_time:.2f} seconds")
```

### **Memory Usage**
```python
import psutil
import os
process = psutil.Process(os.getpid())
print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")
```

## 🚀 **Production Debug Mode**

For production debugging (use carefully):

```bash
# Enable debug mode temporarily
export FLASK_DEBUG=1
export FLASK_ENV=development

# Start with debug logging
python3 app.py
```

**⚠️ Warning**: Never leave debug mode enabled in production!

## 📞 **Debug Support**

If you encounter issues:

1. **Check logs** in the console output
2. **Run test scripts** to isolate the problem
3. **Use VS Code debugger** for step-by-step debugging
4. **Check SAP connectivity** with test scripts
5. **Verify file formats** and data integrity

## 🎉 **Debug Success Indicators**

When debugging is working correctly:

- ✅ Application starts without errors
- ✅ SAP connection test passes
- ✅ File uploads process successfully
- ✅ Measuring points are formatted correctly
- ✅ Results display properly in the web interface

Happy debugging! 🐛➡️✅
