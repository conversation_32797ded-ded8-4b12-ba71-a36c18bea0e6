#!/bin/bash

# Docker setup script for SAP RFC testing

set -e

echo "=========================================="
echo "SAP RFC Docker Setup Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_info "Docker and Docker Compose are available"

# Create necessary directories
print_info "Creating necessary directories..."
mkdir -p logs traces nwrfcsdk

# Check for SAP NW RFC SDK
if [ ! -d "nwrfcsdk" ] || [ -z "$(ls -A nwrfcsdk)" ]; then
    print_warning "SAP NW RFC SDK not found in ./nwrfcsdk/"
    echo ""
    echo "To set up the SAP NW RFC SDK:"
    echo "1. Download SAP NW RFC SDK from SAP Support Portal"
    echo "2. Extract it to ./nwrfcsdk/ directory"
    echo "3. The structure should be:"
    echo "   ./nwrfcsdk/lib/"
    echo "   ./nwrfcsdk/include/"
    echo "   ./nwrfcsdk/bin/"
    echo ""
    read -p "Do you want to continue without SAP NW RFC SDK? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Please set up SAP NW RFC SDK and run this script again"
        exit 1
    fi
    print_warning "Continuing without SAP NW RFC SDK (PyRFC installation may fail)"
fi

# Check for SAP environment configuration
if [ ! -f "sap_env.sh" ]; then
    print_warning "sap_env.sh not found"
    if [ -f "sap_env_template.sh" ]; then
        print_info "Copying template to sap_env.sh"
        cp sap_env_template.sh sap_env.sh
        print_warning "Please edit sap_env.sh with your SAP connection details"
    else
        print_info "Creating sap_env.sh template"
        cat > sap_env.sh << 'EOF'
#!/bin/bash
export SAP_ASHOST="your_sap_host"
export SAP_SYSNR="00"
export SAP_CLIENT="100"
export SAP_USER="your_username"
export SAP_PASSWD="your_password"
export SAP_LANG="EN"
EOF
        chmod +x sap_env.sh
        print_warning "Please edit sap_env.sh with your SAP connection details"
    fi
fi

# Build Docker image
print_info "Building Docker image for Intel/AMD64 platform..."
if docker-compose build; then
    print_success "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Show available services
echo ""
print_info "Available Docker services:"
echo "  1. sap-rfc-test       - Standard testing (RFC_NO_DECIMAL_CONV=1)"
echo "  2. sap-rfc-test-trace - With RFC tracing enabled"
echo "  3. sap-rfc-test-alt   - Alternative config (RFC_NO_DECIMAL_CONV=0)"

echo ""
print_info "Usage examples:"
echo "  # Start standard test container"
echo "  docker-compose run --rm sap-rfc-test"
echo ""
echo "  # Start with tracing"
echo "  docker-compose run --rm sap-rfc-test-trace"
echo ""
echo "  # Run the RFC test directly"
echo "  docker-compose run --rm sap-rfc-test python3 test_rfc.py"
echo ""
echo "  # Interactive shell"
echo "  docker-compose run --rm sap-rfc-test bash"

echo ""
print_success "Docker setup completed!"
print_info "Next steps:"
echo "  1. Edit sap_env.sh with your SAP connection details"
echo "  2. Run: docker-compose run --rm sap-rfc-test python3 test_rfc.py"
