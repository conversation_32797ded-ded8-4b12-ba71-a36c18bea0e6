#!/usr/bin/env python3
"""
Test script for Phase 2 validation functionality.
Tests the complete validation workflow including date/time parsing and hours validation.
"""

import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from validation_utils import MeasurementValidator, validate_measurement_records
from sap_utils_mock import MockSAPRFCClient, get_sap_connection_params


class TestSAPClient:
    """Test SAP client with controlled measurement history data."""
    
    def __init__(self, mock_data: Dict[str, Dict[str, Any]]):
        """
        Initialize test client with mock measurement history data.
        
        Args:
            mock_data: Dictionary mapping meas_point to measurement history data
        """
        self.mock_data = mock_data
        self.connected = True
    
    def connect(self):
        return True, "Test connection successful"
    
    def disconnect(self):
        pass
    
    def get_measurement_history(self, meas_point: str):
        """Return mock measurement history for testing."""
        # Convert to alphaIN format for lookup
        from sap_utils_mock import alphaIN
        meas_point_formatted = alphaIN(meas_point, 12)

        if meas_point_formatted in self.mock_data:
            return True, "Success", self.mock_data[meas_point_formatted]
        else:
            return False, f"No measurement history found for {meas_point}", None


def test_date_time_parsing():
    """Test date and time parsing functionality."""
    print("=" * 60)
    print("TESTING DATE/TIME PARSING")
    print("=" * 60)
    
    # Create a test validator
    test_client = TestSAPClient({})
    validator = MeasurementValidator(test_client)
    
    # Test cases for date/time parsing
    test_cases = [
        # Excel format, Expected datetime
        ("2/2/25", "8:40AM", "2025-02-02 08:40:00"),
        ("2/2/2025", "8:40AM", "2025-02-02 08:40:00"),
        ("2025-02-02", "08:40", "2025-02-02 08:40:00"),
        ("2025-02-02", "08:40:00", "2025-02-02 08:40:00"),
        ("12/31/24", "11:59PM", "2024-12-31 23:59:00"),
    ]
    
    print("Testing Excel date/time parsing:")
    for date_str, time_str, expected in test_cases:
        result = validator._parse_excel_datetime(date_str, time_str)
        expected_dt = datetime.strptime(expected, "%Y-%m-%d %H:%M:%S")
        
        if result and result == expected_dt:
            print(f"✅ '{date_str}' + '{time_str}' -> {result}")
        else:
            print(f"❌ '{date_str}' + '{time_str}' -> {result} (expected {expected_dt})")
    
    print("\nTesting SAP date/time parsing:")
    sap_test_cases = [
        ("20250202", "084000", "2025-02-02 08:40:00"),  # SAP format: YYYYMMDD, HHMMSS
        ("20241231", "235959", "2024-12-31 23:59:59"),
    ]
    
    for date_str, time_str, expected in sap_test_cases:
        result = validator._parse_sap_datetime(date_str, time_str)
        expected_dt = datetime.strptime(expected, "%Y-%m-%d %H:%M:%S")
        
        if result and result == expected_dt:
            print(f"✅ SAP '{date_str}' + '{time_str}' -> {result}")
        else:
            print(f"❌ SAP '{date_str}' + '{time_str}' -> {result} (expected {expected_dt})")


def test_hours_validation_logic():
    """Test the hours validation logic."""
    print("\n" + "=" * 60)
    print("TESTING HOURS VALIDATION LOGIC")
    print("=" * 60)
    
    # Create test scenarios
    base_date = datetime.now() - timedelta(days=1)  # Yesterday
    sap_date = base_date.strftime("%Y%m%d")  # SAP format: YYYYMMDD
    sap_time = base_date.strftime("%H%M%S")  # SAP format: HHMMSS
    
    test_scenarios = [
        {
            "name": "Valid scenario - time diff >= hours diff",
            "curr_hrs": 65024.0,
            "entry_date": (base_date + timedelta(hours=25)).strftime("%m/%d/%y"),
            "entry_time": (base_date + timedelta(hours=25)).strftime("%I:%M%p"),
            "sap_reading": "65000",
            "expected_valid": True
        },
        {
            "name": "Invalid scenario - time diff < hours diff",
            "curr_hrs": 65024.0,
            "entry_date": (base_date + timedelta(hours=10)).strftime("%m/%d/%y"),
            "entry_time": (base_date + timedelta(hours=10)).strftime("%I:%M%p"),
            "sap_reading": "65000",
            "expected_valid": False
        },
        {
            "name": "Edge case - exact match",
            "curr_hrs": 65024.0,
            "entry_date": (base_date + timedelta(hours=24, minutes=5)).strftime("%m/%d/%y"),
            "entry_time": (base_date + timedelta(hours=24, minutes=5)).strftime("%I:%M%p"),
            "sap_reading": "65000",
            "expected_valid": True
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\nTesting: {scenario['name']}")
        
        # Create test client with mock data
        from sap_utils_mock import alphaIN
        mock_data = {
            alphaIN("1000", 12): {
                'MDOCM': '00000000000000000325',
                'IDATE': sap_date,
                'READG': scenario['sap_reading'],
                'ITIME': sap_time
            }
        }
        
        test_client = TestSAPClient(mock_data)
        validator = MeasurementValidator(test_client)
        
        # Test the validation
        is_valid, error_msg = validator._validate_hours_against_history(
            scenario['curr_hrs'],
            scenario['entry_date'],
            scenario['entry_time'],
            sap_date,
            sap_time,
            scenario['sap_reading']
        )
        
        if is_valid == scenario['expected_valid']:
            print(f"✅ Result: {'Valid' if is_valid else 'Invalid'} - {error_msg}")
        else:
            print(f"❌ Expected {'Valid' if scenario['expected_valid'] else 'Invalid'}, got {'Valid' if is_valid else 'Invalid'}")
            print(f"   Message: {error_msg}")


def test_complete_validation_workflow():
    """Test the complete validation workflow with sample records."""
    print("\n" + "=" * 60)
    print("TESTING COMPLETE VALIDATION WORKFLOW")
    print("=" * 60)
    
    # Create mock measurement history data
    from sap_utils_mock import alphaIN
    base_date = datetime.now() - timedelta(days=2)
    mock_data = {
        alphaIN("1000", 12): {
            'MDOCM': '00000000000000000325',
            'IDATE': base_date.strftime("%Y%m%d"),  # SAP format: YYYYMMDD
            'READG': '65000',
            'ITIME': base_date.strftime("%H%M%S")  # SAP format: HHMMSS
        },
        alphaIN("1001", 12): {
            'MDOCM': '00000000000000000326',
            'IDATE': base_date.strftime("%Y%m%d"),  # SAP format: YYYYMMDD
            'READG': '66000',
            'ITIME': base_date.strftime("%H%M%S")  # SAP format: HHMMSS
        }
    }
    
    # Create test records
    test_records = [
        {
            'row_number': 2,
            'meas_point': '1000',
            'hours': 65025.0,  # Valid: 25 hours more than SAP reading
            'last_entry_date': (base_date + timedelta(hours=30)).strftime("%m/%d/%y"),
            'last_entry_time': (base_date + timedelta(hours=30)).strftime("%I:%M%p"),
            'manufacturer': 'KOMATSU',
            'model': '830E-1AC',
            'unit_id': '201'
        },
        {
            'row_number': 3,
            'meas_point': '1001',
            'hours': 66050.0,  # Invalid: 50 hours more but only 10 hours time diff
            'last_entry_date': (base_date + timedelta(hours=10)).strftime("%m/%d/%y"),
            'last_entry_time': (base_date + timedelta(hours=10)).strftime("%I:%M%p"),
            'manufacturer': 'KOMATSU',
            'model': '830E-1AC',
            'unit_id': '202'
        }
    ]
    
    # Create test client and validate
    test_client = TestSAPClient(mock_data)
    valid_records, invalid_records = validate_measurement_records(test_records, test_client)
    
    print(f"Total records: {len(test_records)}")
    print(f"Valid records: {len(valid_records)}")
    print(f"Invalid records: {len(invalid_records)}")
    
    print("\nValid records:")
    for record in valid_records:
        print(f"  - Row {record['row_number']}: Meas-Point {record['meas_point']}, Hours {record['hours']}")
    
    print("\nInvalid records:")
    for record in invalid_records:
        print(f"  - Row {record['row_number']}: Meas-Point {record['meas_point']}, Hours {record['hours']}")
        print(f"    Error: {record.get('validation_error', 'Unknown error')}")
    
    # Verify expected results
    if len(valid_records) == 1 and len(invalid_records) == 1:
        print("\n✅ Validation workflow test passed!")
    else:
        print("\n❌ Validation workflow test failed!")


def test_mock_sap_integration():
    """Test integration with mock SAP client."""
    print("\n" + "=" * 60)
    print("TESTING MOCK SAP INTEGRATION")
    print("=" * 60)
    
    # Create mock SAP client
    connection_params = get_sap_connection_params()
    sap_client = MockSAPRFCClient(connection_params)
    
    # Connect
    connected, msg = sap_client.connect()
    print(f"Connection: {'✅' if connected else '❌'} {msg}")
    
    if connected:
        # Test get_measurement_history
        success, message, data = sap_client.get_measurement_history("1000")
        print(f"Get measurement history: {'✅' if success else '❌'} {message}")
        
        if success and data:
            print(f"  MDOCM: {data.get('MDOCM')}")
            print(f"  IDATE: {data.get('IDATE')}")
            print(f"  READG: {data.get('READG')}")
            print(f"  ITIME: {data.get('ITIME')}")
        
        # Test validation with mock client - use realistic test data
        # The mock client generates random data, so we need to get it first
        success2, message2, mock_history = sap_client.get_measurement_history("1000")

        if success2 and mock_history:
            # Create a test record that should be valid
            mock_reading = float(mock_history['READG'])
            # Parse SAP format dates and times
            mock_date = datetime.strptime(mock_history['IDATE'], "%Y%m%d")
            mock_time_str = mock_history['ITIME']
            mock_time = datetime.strptime(f"{mock_time_str[:2]}:{mock_time_str[2:4]}:{mock_time_str[4:6]}", "%H:%M:%S").time()
            mock_date_time = datetime.combine(mock_date.date(), mock_time)

            # Create entry that's 25 hours later with 20 more hours reading
            entry_time = mock_date_time + timedelta(hours=25)

            test_record = {
                'row_number': 2,
                'meas_point': '1000',
                'hours': mock_reading + 20,  # 20 hours more than SAP reading
                'last_entry_date': entry_time.strftime("%m/%d/%y"),
                'last_entry_time': entry_time.strftime("%I:%M%p"),
                'manufacturer': 'KOMATSU'
            }
        else:
            # Fallback test record
            test_record = {
                'row_number': 2,
                'meas_point': '1000',
                'hours': 65024.0,
                'last_entry_date': '2/2/25',
                'last_entry_time': '8:40AM',
                'manufacturer': 'KOMATSU'
            }
        
        validator = MeasurementValidator(sap_client)
        is_valid, error_msg = validator.validate_measurement_record(test_record)
        print(f"Record validation: {'✅' if is_valid else '❌'} {error_msg}")
        
        sap_client.disconnect()


if __name__ == "__main__":
    print("Phase 2 Validation Testing")
    print("=" * 60)
    
    try:
        test_date_time_parsing()
        test_hours_validation_logic()
        test_complete_validation_workflow()
        test_mock_sap_integration()
        
        print("\n" + "=" * 60)
        print("ALL TESTS COMPLETED")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
