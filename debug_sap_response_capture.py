#!/usr/bin/env python3
"""
Advanced diagnostic script to capture and analyze SAP response data.
"""

import os
import sys
import json
from typing import Any, Dict

def enable_rfc_tracing():
    """Enable RFC tracing to capture detailed communication."""
    print("=" * 60)
    print("RFC TRACING SETUP")
    print("=" * 60)
    
    # Set RFC trace environment variable
    os.environ['RFC_TRACE'] = '1'
    print("✅ RFC_TRACE=1 enabled")
    
    # Check if trace file will be created
    trace_file = "dev_rfc.log"
    if os.path.exists(trace_file):
        print(f"⚠️  Existing trace file found: {trace_file}")
        print("   Previous trace data will be appended")
    else:
        print(f"📄 New trace file will be created: {trace_file}")
    
    print("\nRFC tracing will capture:")
    print("• Raw RFC communication data")
    print("• Parameter values sent to SAP")
    print("• Response data received from SAP")
    print("• Internal PyRFC processing steps")
    print()

def analyze_pyrfc_internals():
    """Analyze PyRFC internal behavior."""
    print("=" * 60)
    print("PYRFC INTERNAL ANALYSIS")
    print("=" * 60)
    
    try:
        from pyrfc import Connection
        print("✅ PyRFC imported successfully")
        
        # Check PyRFC version
        try:
            import pyrfc
            version = getattr(pyrfc, '__version__', 'Unknown')
            print(f"📦 PyRFC version: {version}")
        except:
            print("⚠️  Could not determine PyRFC version")
        
        # Check if we can access internal conversion functions
        print("\nChecking PyRFC internal conversion behavior:")
        
        # Test values that might cause issues
        test_values = [
            "1234567890",
            "0000001234567",
            "DOC1234567",
            "",
            None,
        ]
        
        for value in test_values:
            print(f"Testing value: {repr(value)}")
            try:
                # Try to understand how PyRFC handles different data types
                if value is not None:
                    str_val = str(value)
                    print(f"  String conversion: '{str_val}'")
                    
                    # Test decimal conversion (this might be where the error occurs)
                    try:
                        from decimal import Decimal
                        decimal_val = Decimal(str_val)
                        print(f"  ✅ Decimal conversion: {decimal_val}")
                    except Exception as e:
                        print(f"  ❌ Decimal conversion failed: {e}")
                else:
                    print(f"  None value - skipping conversions")
            except Exception as e:
                print(f"  ❌ Processing failed: {e}")
            print()
            
    except ImportError as e:
        print(f"❌ PyRFC not available: {e}")

def create_response_interceptor():
    """Create a method to intercept SAP responses."""
    print("=" * 60)
    print("RESPONSE INTERCEPTION STRATEGY")
    print("=" * 60)
    
    print("Strategy to capture SAP response before PyRFC processes it:")
    print()
    print("1. Enable RFC tracing (RFC_TRACE=1)")
    print("   - Captures raw RFC communication")
    print("   - Shows exact data sent/received")
    print()
    print("2. Monkey-patch PyRFC Connection.call method")
    print("   - Intercept calls before internal processing")
    print("   - Log raw response data")
    print()
    print("3. Use try/except around specific response fields")
    print("   - Catch decimal conversion errors per field")
    print("   - Identify which field causes the issue")
    print()
    print("4. Alternative RFC function calls")
    print("   - Try different SAP functions")
    print("   - Use BAPI functions instead of direct RFC")

def create_monkey_patch_example():
    """Create example of monkey-patching PyRFC for debugging."""
    print("\n" + "=" * 60)
    print("MONKEY-PATCH EXAMPLE")
    print("=" * 60)
    
    monkey_patch_code = '''
# Add this to sap_utils.py to intercept responses

def debug_connection_call(original_call):
    """Wrapper for Connection.call to debug responses."""
    def wrapper(self, function_name, **kwargs):
        print(f"🔍 RFC Call: {function_name}")
        print(f"🔍 Parameters: {kwargs}")
        
        try:
            # Call original function
            result = original_call(self, function_name, **kwargs)
            print(f"🔍 Response type: {type(result)}")
            
            if isinstance(result, dict):
                print(f"🔍 Response keys: {list(result.keys())}")
                
                # Try to access each field safely
                for key, value in result.items():
                    try:
                        print(f"🔍 {key}: {repr(value)} (type: {type(value)})")
                        
                        # Test decimal conversion on this field
                        if value is not None:
                            from decimal import Decimal
                            try:
                                decimal_val = Decimal(str(value))
                                print(f"  ✅ Decimal OK: {decimal_val}")
                            except Exception as e:
                                print(f"  ❌ Decimal FAIL: {e}")
                                
                    except Exception as e:
                        print(f"🔍 Error accessing {key}: {e}")
            
            return result
            
        except Exception as e:
            print(f"❌ RFC Call failed: {e}")
            raise
    
    return wrapper

# Apply the monkey patch
from pyrfc import Connection
Connection.call = debug_connection_call(Connection.call)
'''
    
    print("Monkey-patch code to add to sap_utils.py:")
    print(monkey_patch_code)

def show_next_steps():
    """Show next debugging steps."""
    print("=" * 60)
    print("NEXT DEBUGGING STEPS")
    print("=" * 60)
    
    print("To identify the exact cause of the decimal conversion error:")
    print()
    print("1. Enable RFC tracing:")
    print("   export RFC_TRACE=1")
    print("   ./debug.sh --attach")
    print()
    print("2. Upload a file and check dev_rfc.log:")
    print("   tail -f dev_rfc.log")
    print()
    print("3. Look for the response data in the trace:")
    print("   - Find the MEASUREM_DOCUM_RFC_SINGLE_001 call")
    print("   - Check the response section")
    print("   - Identify which field has problematic data")
    print()
    print("4. Common problematic fields in SAP responses:")
    print("   - MEASUREMENT_DOCUMENT (document number)")
    print("   - RETURN_CODE (status code)")
    print("   - MESSAGE (status message)")
    print("   - Numeric fields with special formatting")
    print()
    print("5. Test with different SAP users/systems:")
    print("   - Different users might get different response formats")
    print("   - Different SAP versions handle decimals differently")

def main():
    """Main diagnostic function."""
    print("Advanced SAP Response Debugging")
    print("This script helps identify what in the SAP response causes decimal conversion errors")
    print()
    
    enable_rfc_tracing()
    analyze_pyrfc_internals()
    create_response_interceptor()
    create_monkey_patch_example()
    show_next_steps()
    
    print("\n🎯 IMMEDIATE ACTION:")
    print("1. Run: export RFC_TRACE=1")
    print("2. Run: ./debug.sh --attach")
    print("3. Upload a file")
    print("4. Check: cat dev_rfc.log | grep -A 20 -B 5 'MEASUREM_DOCUM_RFC_SINGLE_001'")
    print("5. Look for the response data that causes the decimal conversion error")

if __name__ == '__main__':
    main()
