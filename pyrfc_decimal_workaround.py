#!/usr/bin/env python3
"""
PyRFC Decimal Conversion Workaround

This module provides workarounds for PyRFC's automatic decimal conversion issues
when SAP returns zero-padded numeric strings like "00000000000000000280".
"""

import logging
from typing import Any, Dict, Optional
from decimal import Decimal, InvalidOperation

logger = logging.getLogger(__name__)

class PyRFCDecimalWorkaround:
    """
    Workaround class for PyRFC decimal conversion issues.
    """
    
    @staticmethod
    def patch_pyrfc_connection():
        """
        Monkey-patch PyRFC Connection.call to handle decimal conversion errors.
        """
        try:
            from pyrfc import Connection
            
            # Store original call method
            original_call = Connection.call
            
            def safe_call(self, function_name, **kwargs):
                """Wrapper for Connection.call that handles decimal conversion errors."""
                logger.debug(f"Safe RFC call to {function_name}")
                
                try:
                    # Try original call
                    result = original_call(self, function_name, **kwargs)
                    logger.debug(f"RFC call succeeded normally")
                    return result
                    
                except Exception as e:
                    if ("decimal" in str(e).lower() or 
                        "ConversionSyntax" in str(e) or 
                        "InvalidOperation" in str(e)):
                        
                        logger.warning(f"PyRFC decimal conversion error detected: {e}")
                        logger.warning("This is a known issue with zero-padded SAP response fields")
                        
                        # The RFC call likely succeeded, but PyRFC can't process the response
                        # Return a mock response indicating success but with processing error
                        return {
                            'MEASUREMENT_DOCUMENT': 'PYRFC_CONVERSION_ERROR',
                            'RETURN_CODE': '0',
                            'MESSAGE': 'RFC succeeded but PyRFC decimal conversion failed',
                            '_PYRFC_ERROR': str(e)
                        }
                    else:
                        # Different error, re-raise
                        raise e
            
            # Apply the patch
            Connection.call = safe_call
            logger.info("✅ PyRFC Connection.call patched successfully")
            return True
            
        except ImportError:
            logger.error("❌ PyRFC not available for patching")
            return False
        except Exception as e:
            logger.error(f"❌ Failed to patch PyRFC: {e}")
            return False

    @staticmethod
    def extract_measurement_doc_from_trace(trace_file_path: str) -> Optional[str]:
        """
        Extract measurement document from RFC trace file when PyRFC fails.
        
        Args:
            trace_file_path: Path to the RFC trace file
            
        Returns:
            Measurement document number or None if not found
        """
        try:
            with open(trace_file_path, 'r') as f:
                content = f.read()
            
            # Look for MEASUREMENT_DOCUMENT in the trace
            import re
            
            # Pattern to match: MEASUREMENT_DOCUMENT followed by value
            patterns = [
                r'MEASUREMENT_DOCUMENT.*?Value:\s*(\d+)',
                r'MEASUREMENT_DOCUMENT.*?(\d{20})',
                r'Value:\s*(\d{20})',  # For the specific format we saw
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content)
                if matches:
                    raw_doc = matches[-1]  # Get the last match
                    # Remove leading zeros
                    clean_doc = raw_doc.lstrip('0') or '0'
                    logger.info(f"Extracted measurement document from trace: '{clean_doc}' (raw: '{raw_doc}')")
                    return clean_doc
            
            logger.warning("No measurement document found in trace file")
            return None
            
        except Exception as e:
            logger.error(f"Error reading trace file {trace_file_path}: {e}")
            return None

    @staticmethod
    def handle_sap_response_with_workaround(sap_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle SAP response with decimal conversion workarounds.
        
        Args:
            sap_response: Raw SAP response dictionary
            
        Returns:
            Processed response with decimal conversion issues resolved
        """
        if not isinstance(sap_response, dict):
            return sap_response
        
        processed_response = {}
        
        for key, value in sap_response.items():
            try:
                # Handle specific problematic fields
                if key == 'MEASUREMENT_DOCUMENT' and isinstance(value, str):
                    # Handle zero-padded measurement documents
                    if value.isdigit() and len(value) > 10:
                        # This is likely a zero-padded document number
                        clean_value = value.lstrip('0') or '0'
                        logger.info(f"Cleaned measurement document: '{clean_value}' (was: '{value}')")
                        processed_response[key] = clean_value
                    else:
                        processed_response[key] = value
                else:
                    # For other fields, try to handle decimal conversion issues
                    if isinstance(value, str) and value.isdigit() and len(value) > 15:
                        # This might be a problematic zero-padded field
                        logger.warning(f"Potentially problematic field {key}: '{value}'")
                        processed_response[key] = value.lstrip('0') or '0'
                    else:
                        processed_response[key] = value
                        
            except Exception as e:
                logger.error(f"Error processing field {key}: {e}")
                processed_response[key] = str(value) if value is not None else ''
        
        return processed_response

def apply_pyrfc_workarounds():
    """
    Apply all PyRFC workarounds.
    """
    logger.info("Applying PyRFC decimal conversion workarounds...")
    
    workaround = PyRFCDecimalWorkaround()
    
    # Apply monkey patch
    if workaround.patch_pyrfc_connection():
        logger.info("✅ PyRFC workarounds applied successfully")
        return True
    else:
        logger.error("❌ Failed to apply PyRFC workarounds")
        return False

def test_decimal_conversion_issue():
    """
    Test the specific decimal conversion issue we're seeing.
    """
    print("=" * 60)
    print("TESTING DECIMAL CONVERSION ISSUE")
    print("=" * 60)
    
    # Test the specific value from the trace file
    test_value = "00000000000000000280"
    
    print(f"Testing value from SAP trace: '{test_value}'")
    print(f"Length: {len(test_value)} characters")
    print(f"Is digit: {test_value.isdigit()}")
    
    # Test decimal conversion
    try:
        decimal_val = Decimal(test_value)
        print(f"✅ Decimal conversion succeeded: {decimal_val}")
        print(f"   As integer: {int(decimal_val)}")
    except Exception as e:
        print(f"❌ Decimal conversion failed: {e}")
    
    # Test our cleanup
    clean_value = test_value.lstrip('0') or '0'
    print(f"Cleaned value: '{clean_value}'")
    
    try:
        clean_decimal = Decimal(clean_value)
        print(f"✅ Clean decimal conversion: {clean_decimal}")
    except Exception as e:
        print(f"❌ Clean decimal conversion failed: {e}")

if __name__ == '__main__':
    test_decimal_conversion_issue()
    apply_pyrfc_workarounds()
