#!/usr/bin/env python3
"""
Test script to verify datetime handling works correctly.
Tests both string and datetime object parsing.
"""

import sys
import os
from datetime import datetime
import pandas as pd

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from validation_utils import MeasurementValidator


class TestSAPClient:
    """Test SAP client for datetime testing."""
    
    def __init__(self):
        self.connected = True
    
    def connect(self):
        return True, "Test connection successful"
    
    def disconnect(self):
        pass
    
    def get_measurement_history(self, meas_point: str):
        """Return test SAP response."""
        measurement_data = {
            'MDOCM': '00000000000000000322',
            'IDATE': '20250721',
            'READG': '65033',
            'ITIME': '114505'
        }
        return True, "Success", measurement_data


def test_datetime_parsing():
    """Test various datetime input formats."""
    print("=" * 60)
    print("TESTING DATETIME PARSING")
    print("=" * 60)
    
    test_client = TestSAPClient()
    validator = MeasurementValidator(test_client)
    
    # Test cases with different input types
    test_cases = [
        {
            "name": "String date",
            "date": "7/21/25",
            "time": "2:00PM",
            "expected_success": True
        },
        {
            "name": "Datetime object",
            "date": datetime(2025, 7, 21),
            "time": "2:00PM",
            "expected_success": True
        },
        {
            "name": "Pandas Timestamp",
            "date": pd.Timestamp('2025-07-21'),
            "time": "2:00PM",
            "expected_success": True
        },
        {
            "name": "ISO format string",
            "date": "2025-07-21",
            "time": "14:00",
            "expected_success": True
        }
    ]
    
    for test_case in test_cases:
        print(f"\nTesting: {test_case['name']}")
        print(f"  Date: {test_case['date']} (type: {type(test_case['date'])})")
        print(f"  Time: {test_case['time']}")
        
        try:
            result = validator._parse_excel_datetime(test_case['date'], test_case['time'])
            
            if result:
                print(f"  ✅ Parsed successfully: {result}")
            else:
                print(f"  ❌ Failed to parse")
                
        except Exception as e:
            print(f"  ❌ Error: {e}")


def test_full_validation():
    """Test full validation with datetime objects."""
    print("\n" + "=" * 60)
    print("TESTING FULL VALIDATION WITH DATETIME OBJECTS")
    print("=" * 60)
    
    test_client = TestSAPClient()
    validator = MeasurementValidator(test_client)
    
    # Test record with datetime object (simulating pandas output)
    test_record = {
        'row_number': 2,
        'meas_point': '1000',
        'hours': 65050.0,
        'last_entry_date': pd.Timestamp('2025-07-21 14:00:00'),  # Pandas Timestamp
        'last_entry_time': '2:00PM',
        'manufacturer': 'KOMATSU'
    }
    
    print("Test record:")
    print(f"  Meas-Point: {test_record['meas_point']}")
    print(f"  Hours: {test_record['hours']}")
    print(f"  Last Entry Date: {test_record['last_entry_date']} (type: {type(test_record['last_entry_date'])})")
    print(f"  Last Entry Time: {test_record['last_entry_time']}")
    
    # Test validation
    is_valid, error_msg = validator.validate_measurement_record(test_record)
    
    print(f"\nValidation Result:")
    print(f"  Valid: {'✅' if is_valid else '❌'}")
    print(f"  Message: {error_msg}")


if __name__ == "__main__":
    print("DateTime Handling Test")
    print("=" * 60)
    
    try:
        test_datetime_parsing()
        test_full_validation()
        
        print("\n" + "=" * 60)
        print("DATETIME HANDLING TESTS COMPLETED")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
