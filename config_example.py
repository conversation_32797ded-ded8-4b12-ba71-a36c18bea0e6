"""
Example configuration file for SAP connection parameters.
Copy this to config.py and update with your actual SAP system details.
"""

import os

# SAP Connection Parameters
# These should be set as environment variables in production
SAP_CONFIG = {
    'ashost': os.getenv('SAP_ASHOST', 'your-sap-server.company.com'),
    'sysnr': os.getenv('SAP_SYSNR', '00'),
    'client': os.getenv('SAP_CLIENT', '100'),
    'user': os.getenv('SAP_USER', 'your-username'),
    'passwd': os.getenv('SAP_PASSWD', 'your-password'),
    'lang': os.getenv('SAP_LANG', 'EN'),
    # Optional: SAP Router string if needed
    # 'saprouter': '/H/your-router-host/S/3299/H/your-sap-server/S/3200'
}

# Flask Configuration
FLASK_CONFIG = {
    'SECRET_KEY': os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production'),
    'DEBUG': os.getenv('FLASK_DEBUG', 'False').lower() == 'true',
    'PORT': int(os.getenv('PORT', 5000)),
    'HOST': os.getenv('HOST', '0.0.0.0')
}

# File Upload Configuration
UPLOAD_CONFIG = {
    'MAX_CONTENT_LENGTH': 16 * 1024 * 1024,  # 16MB
    'ALLOWED_EXTENSIONS': {'xlsx', 'xls'}
}

# Logging Configuration
LOGGING_CONFIG = {
    'level': os.getenv('LOG_LEVEL', 'INFO'),
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
}
