#!/bin/bash

# Measuring Point Upload Application Startup Script
# This script loads SAP environment variables and starts the Flask application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DEFAULT_PORT=5000
SAP_ENV_FILE="sap_env.sh"
APP_FILE="app.py"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is available
is_port_available() {
    local port=$1
    ! lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1
}

# Function to find available port
find_available_port() {
    local port=$DEFAULT_PORT
    while ! is_port_available $port; do
        print_warning "Port $port is in use, trying $((port + 1))"
        port=$((port + 1))
        if [ $port -gt 5010 ]; then
            print_error "Could not find available port between $DEFAULT_PORT and 5010"
            exit 1
        fi
    done
    echo $port
}

# Function to cleanup on exit
cleanup() {
    if [ ! -z "$APP_PID" ]; then
        print_status "Shutting down application (PID: $APP_PID)..."
        kill $APP_PID 2>/dev/null || true
    fi
}

# Set up trap for cleanup
trap cleanup EXIT INT TERM

# Main function
main() {
    print_header "=================================================="
    print_header "  Measuring Point Upload Application Startup"
    print_header "=================================================="
    
    # Check if Python is available
    if ! command_exists python3; then
        print_error "Python 3 is not installed or not in PATH"
        exit 1
    fi
    
    print_status "Python version: $(python3 --version)"
    
    # Check if application file exists
    if [ ! -f "$APP_FILE" ]; then
        print_error "Application file '$APP_FILE' not found"
        exit 1
    fi
    
    # Check if SAP environment file exists
    if [ ! -f "$SAP_ENV_FILE" ]; then
        print_warning "SAP environment file '$SAP_ENV_FILE' not found"
        print_warning "Application will run with mock SAP functionality"
        print_warning "Run 'python3 setup_sap_connection.py' to configure SAP connection"
    else
        print_status "Loading SAP environment variables from '$SAP_ENV_FILE'..."
        source ./$SAP_ENV_FILE
        
        # Verify SAP variables are loaded
        if [ -z "$SAP_ASHOST" ] || [ -z "$SAP_USER" ]; then
            print_warning "SAP environment variables not properly loaded"
            print_warning "Application will run with mock SAP functionality"
        else
            print_status "SAP environment loaded successfully"
            print_status "SAP Host: $SAP_ASHOST"
            print_status "SAP Client: $SAP_CLIENT"
            print_status "SAP User: $SAP_USER"
        fi
    fi
    
    # Find available port
    PORT=$(find_available_port)
    export PORT=$PORT
    
    print_status "Starting application on port $PORT..."
    print_status "Application will be available at: http://localhost:$PORT"
    
    # Check if requirements are installed
    print_status "Checking Python dependencies..."
    if ! python3 -c "import flask, pandas, openpyxl" 2>/dev/null; then
        print_warning "Some dependencies might be missing"
        print_status "Installing basic requirements (excluding PyRFC)..."
        pip3 install flask pandas openpyxl
    fi

    # Check PyRFC separately (it requires SAP RFC SDK environment)
    if ! python3 -c "import pyrfc" 2>/dev/null; then
        print_warning "PyRFC not available - application will use mock SAP functionality"
        print_warning "To use real SAP connection:"
        print_warning "  1. Ensure SAP RFC SDK environment variables are set"
        print_warning "  2. Run: pip3 install pyrfc"
    else
        print_status "PyRFC is available - real SAP connection will be used"
    fi
    
    # Start the application
    print_header "=================================================="
    print_header "  Starting Flask Application"
    print_header "=================================================="
    
    python3 $APP_FILE &
    APP_PID=$!
    
    # Wait a moment for the app to start
    sleep 2
    
    # Check if the application started successfully
    if kill -0 $APP_PID 2>/dev/null; then
        print_status "Application started successfully (PID: $APP_PID)"
        print_status "Access the application at: http://localhost:$PORT"
        print_status "API endpoints:"
        print_status "  - Health check: http://localhost:$PORT/health"
        print_status "  - Status: http://localhost:$PORT/status"
        print_status "  - Upload API: http://localhost:$PORT/api/upload"
        
        # Test the health endpoint
        sleep 1
        if command_exists curl; then
            print_status "Testing application health..."
            if curl -s http://localhost:$PORT/health >/dev/null; then
                print_status "✅ Application is healthy and responding"
            else
                print_warning "Application might not be fully ready yet"
            fi
        fi
        
        print_header "=================================================="
        print_header "  Application is running!"
        print_header "  Press Ctrl+C to stop"
        print_header "=================================================="
        
        # Wait for the application to finish
        wait $APP_PID
    else
        print_error "Failed to start application"
        exit 1
    fi
}

# Handle command line arguments
case "${1:-}" in
    -h|--help)
        echo "Usage: $0 [OPTIONS]"
        echo ""
        echo "Options:"
        echo "  -h, --help     Show this help message"
        echo "  -p, --port     Specify port number (default: $DEFAULT_PORT)"
        echo ""
        echo "Environment files:"
        echo "  $SAP_ENV_FILE    SAP connection parameters"
        echo ""
        echo "Examples:"
        echo "  $0              # Start with default settings"
        echo "  $0 -p 8080      # Start on port 8080"
        exit 0
        ;;
    -p|--port)
        if [ -z "$2" ]; then
            print_error "Port number required"
            exit 1
        fi
        DEFAULT_PORT=$2
        shift 2
        ;;
    "")
        # No arguments, proceed normally
        ;;
    *)
        print_error "Unknown option: $1"
        print_error "Use -h or --help for usage information"
        exit 1
        ;;
esac

# Run main function
main "$@"
